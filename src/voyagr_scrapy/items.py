import scrapy
from scrapy import Item, Field


class MarketItem(scrapy.Item):
    market_id = Field()
    name = Field()
    description = Field()
    category = Field()
    location = Field()
    status = Field()
    created_date = Field()
    updated_date = Field()
    url = Field()
    scraped_at = Field()


class ProductItem(scrapy.Item):
    product_id = Field()
    name = Field()
    description = Field()
    price = Field()
    currency = Field()
    category = Field()
    subcategory = Field()
    brand = Field()
    vendor = Field()
    availability = Field()
    stock_quantity = Field()
    images = Field()
    specifications = Field()
    rating = Field()
    reviews_count = Field()
    url = Field()
    market_id = Field()
    scraped_at = Field()


class ProductViewItem(scrapy.Item):
    product_id = Field()
    product_name = Field()
    developer_name = Field()
    developer_id = Field()
    current_version = Field()
    last_update = Field()
    publish_date = Field()
    activation_count = Field()
    review_count = Field()
    comments_count = Field()
    demo_downloaded = Field()  # Added demo download count
    rating_overall = Field()
    rating_description_quality = Field()
    rating_reliability = Field()
    rating_support = Field()
    product_extraction_timestamp = Field()
    url = Field()
    scraped_at = Field()


class UserItem(scrapy.Item):
    user_id = Field()
    username = Field()
    display_name = Field()
    profile_url = Field()
    avatar_url = Field()
    registration_date = Field()
    last_active = Field()
    reputation_score = Field()
    total_reviews = Field()
    total_products = Field()
    location = Field()
    verified = Field()
    account_type = Field()
    social_links = Field()
    contact_info = Field()
    market_id = Field()
    scraped_at = Field()


class DeveloperItem(scrapy.Item):
    developer_id = Field()
    name = Field()
    company_name = Field()
    profile_url = Field()
    website = Field()
    description = Field()
    established_date = Field()
    location = Field()
    contact_info = Field()
    social_links = Field()
    total_products = Field()
    product_categories = Field()
    rating = Field()
    reviews_count = Field()
    verified = Field()
    certifications = Field()
    portfolio = Field()
    market_id = Field()
    scraped_at = Field()
