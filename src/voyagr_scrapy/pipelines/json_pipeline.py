import json
import os
from datetime import datetime
from scrapy.exceptions import DropItem


class JsonExportPipeline:
    def __init__(self, output_dir='output'):
        self.output_dir = output_dir
        self.files = {}
        
    @classmethod
    def from_crawler(cls, crawler):
        return cls(
            output_dir=crawler.settings.get('JSON_OUTPUT_DIR', 'output')
        )
    
    def open_spider(self, spider):
        os.makedirs(self.output_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{spider.name}_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        self.files[spider.name] = open(filepath, 'w', encoding='utf-8')
        self.files[spider.name].write('[\n')
        self.item_count = 0
    
    def close_spider(self, spider):
        if spider.name in self.files:
            self.files[spider.name].write('\n]')
            self.files[spider.name].close()
            spider.logger.info(f"Exported {self.item_count} items to {self.files[spider.name].name}")
    
    def process_item(self, item, spider):
        if self.item_count > 0:
            self.files[spider.name].write(',\n')
        
        line = json.dumps(dict(item), indent=2, ensure_ascii=False)
        self.files[spider.name].write(line)
        self.item_count += 1
        
        return item


class ValidationPipeline:
    def process_item(self, item, spider):
        # Basic validation - ensure required fields exist
        required_fields = {
            'market': ['name', 'url'],
            'product': ['name', 'url'],
            'user': ['username', 'url'],
            'developer': ['name', 'profile_url']
        }
        
        spider_required = required_fields.get(spider.name, [])
        for field in spider_required:
            if not item.get(field):
                raise DropItem(f"Missing required field: {field} in {spider.name} spider")
        
        return item


class DeduplicationPipeline:
    def __init__(self):
        self.ids_seen = set()
    
    def process_item(self, item, spider):
        # Create unique identifier based on spider type
        if spider.name == 'market':
            unique_id = f"{spider.name}_{item.get('market_id', item.get('url'))}"
        elif spider.name == 'product':
            unique_id = f"{spider.name}_{item.get('product_id', item.get('url'))}"
        elif spider.name == 'user':
            unique_id = f"{spider.name}_{item.get('user_id', item.get('username'))}"
        elif spider.name == 'developer':
            unique_id = f"{spider.name}_{item.get('developer_id', item.get('name'))}"
        else:
            unique_id = f"{spider.name}_{item.get('url', str(hash(str(item))))}"
        
        if unique_id in self.ids_seen:
            raise DropItem(f"Duplicate item found: {unique_id}")
        else:
            self.ids_seen.add(unique_id)
            return item