BOT_NAME = 'voyagr_scrapy'

SPIDER_MODULES = ['voyagr_scrapy.spiders']
NEWSPIDER_MODULE = 'voyagr_scrapy.spiders'

# Obey robots.txt rules
ROBOTSTXT_OBEY = False

# Configure pipelines
ITEM_PIPELINES = {
    'voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline': 300,
    'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline': 400,
    'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline': 500,
}

# Configure a delay for requests (be respectful)
DOWNLOAD_DELAY = 1
RANDOMIZE_DOWNLOAD_DELAY = 0.5

# Configure maximum concurrent requests
CONCURRENT_REQUESTS = 16
CONCURRENT_REQUESTS_PER_DOMAIN = 8

# Configure retry settings
RETRY_TIMES = 3
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]

# Configure timeout
DOWNLOAD_TIMEOUT = 30

# Configure user agent
USER_AGENT = 'voyagr_scrapy (+http://www.yourdomain.com)'

# Configure headers
DEFAULT_REQUEST_HEADERS = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'en',
    'Accept-Encoding': 'gzip, deflate',
}

# Configure output directory
JSON_OUTPUT_DIR = 'output'

# Configure logging
LOG_LEVEL = 'INFO'
LOG_FILE = 'scrapy.log'

# Configure cache (optional)
HTTPCACHE_ENABLED = True
HTTPCACHE_EXPIRATION_SECS = 3600
HTTPCACHE_DIR = 'httpcache'

# Configure autothrottle extension
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 1
AUTOTHROTTLE_MAX_DELAY = 60
AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0
AUTOTHROTTLE_DEBUG = False

# Configure telnet console (disable for security)
TELNETCONSOLE_ENABLED = False

# Configure spider middlewares
SPIDER_MIDDLEWARES = {
    'voyagr_scrapy.middlewares.SpiderMiddleware': 543,
}

# Configure downloader middlewares
DOWNLOADER_MIDDLEWARES = {
    'voyagr_scrapy.middlewares.RotateUserAgentMiddleware': 400,
    'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,
}

# Request fingerprinting implementation
REQUEST_FINGERPRINTER_IMPLEMENTATION = '2.7'

# Disable Twisted reactor deprecation warnings
TWISTED_REACTOR = 'twisted.internet.asyncioreactor.AsyncioSelectorReactor'