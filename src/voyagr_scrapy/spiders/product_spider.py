import scrapy
from datetime import datetime
from voyagr_scrapy.items import ProductItem


class ProductSpider(scrapy.Spider):
    name = 'product'
    allowed_domains = []
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(ProductSpider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = ProductItem()
        item['url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        
        # Extract product information - customize based on target sites
        item['product_id'] = self.extract_product_id(response)
        item['name'] = self.extract_name(response)
        item['description'] = self.extract_description(response)
        item['price'] = self.extract_price(response)
        item['currency'] = self.extract_currency(response)
        item['category'] = self.extract_category(response)
        item['subcategory'] = self.extract_subcategory(response)
        item['brand'] = self.extract_brand(response)
        item['vendor'] = self.extract_vendor(response)
        item['availability'] = self.extract_availability(response)
        item['stock_quantity'] = self.extract_stock_quantity(response)
        item['images'] = self.extract_images(response)
        item['specifications'] = self.extract_specifications(response)
        item['rating'] = self.extract_rating(response)
        item['reviews_count'] = self.extract_reviews_count(response)
        item['market_id'] = self.extract_market_id(response)
        
        yield item

    def extract_product_id(self, response):
        return response.css('::attr(data-product-id)').get() or response.url.split('/')[-1]

    def extract_name(self, response):
        return response.css('h1::text, .product-name::text, .title::text').get()

    def extract_description(self, response):
        return ' '.join(response.css('.description::text, .product-description::text, .details::text').getall())

    def extract_price(self, response):
        price_text = response.css('.price::text, .cost::text, .amount::text').get()
        if price_text:
            import re
            price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
            return float(price_match.group()) if price_match else None
        return None

    def extract_currency(self, response):
        price_text = response.css('.price::text, .cost::text, .amount::text').get()
        if price_text:
            import re
            currency_match = re.search(r'[^\d\s,\.]+', price_text)
            return currency_match.group() if currency_match else 'USD'
        return 'USD'

    def extract_category(self, response):
        return response.css('.category::text, .breadcrumb li:last-child::text').get()

    def extract_subcategory(self, response):
        return response.css('.subcategory::text, .breadcrumb li:nth-last-child(2)::text').get()

    def extract_brand(self, response):
        return response.css('.brand::text, .manufacturer::text').get()

    def extract_vendor(self, response):
        return response.css('.vendor::text, .seller::text').get()

    def extract_availability(self, response):
        return response.css('.availability::text, .stock-status::text').get()

    def extract_stock_quantity(self, response):
        stock_text = response.css('.stock::text, .quantity::text').get()
        if stock_text:
            import re
            stock_match = re.search(r'\d+', stock_text)
            return int(stock_match.group()) if stock_match else None
        return None

    def extract_images(self, response):
        return response.css('.product-image img::attr(src), .gallery img::attr(src)').getall()

    def extract_specifications(self, response):
        specs = {}
        spec_items = response.css('.specifications li, .specs tr')
        for item in spec_items:
            key = item.css('.spec-name::text, td:first-child::text').get()
            value = item.css('.spec-value::text, td:last-child::text').get()
            if key and value:
                specs[key.strip()] = value.strip()
        return specs

    def extract_rating(self, response):
        rating_text = response.css('.rating::text, .stars::attr(data-rating)').get()
        if rating_text:
            import re
            rating_match = re.search(r'[\d\.]+', rating_text)
            return float(rating_match.group()) if rating_match else None
        return None

    def extract_reviews_count(self, response):
        reviews_text = response.css('.reviews-count::text, .review-count::text').get()
        if reviews_text:
            import re
            reviews_match = re.search(r'\d+', reviews_text)
            return int(reviews_match.group()) if reviews_match else None
        return None

    def extract_market_id(self, response):
        return response.css('::attr(data-market-id)').get() or 'unknown'