import scrapy
from datetime import datetime
from voyagr_scrapy.items import ProductViewItem
import re


class ProductViewSpider(scrapy.Spider):
    name = 'product_view'
    allowed_domains = ['mql5.com']
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(ProductViewSpider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = ProductViewItem()
        item['url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        item['product_extraction_timestamp'] = datetime.now().strftime('%d/%m/%Y %H:%M:%S')
        
        # Extract product information from the detailed product page
        item['product_id'] = self.extract_product_id(response)
        item['product_name'] = self.extract_product_name(response)
        item['developer_name'] = self.extract_developer_name(response)
        item['developer_id'] = self.extract_developer_id(response)
        item['current_version'] = self.extract_current_version(response)
        item['last_update'] = self.extract_last_update(response)
        item['publish_date'] = self.extract_publish_date(response)
        item['activation_count'] = self.extract_activation_count(response)
        item['review_count'] = self.extract_review_count(response)
        item['comments_count'] = self.extract_comments_count(response)
        item['rating_overall'] = self.extract_rating_overall(response)
        item['rating_description_quality'] = self.extract_rating_description_quality(response)
        item['rating_reliability'] = self.extract_rating_reliability(response)
        item['rating_support'] = self.extract_rating_support(response)
        
        yield item

    def extract_product_id(self, response):
        # Extract product ID from URL like "/en/market/product/118805"
        url_match = re.search(r'/product/(\d+)', response.url)
        if url_match:
            return url_match.group(1)
        return None

    def extract_product_name(self, response):
        # Extract product name from page title or main heading
        selectors = [
            'h1::text',
            '.product-title::text',
            '.product-name::text',
            'title::text'
        ]
        
        for selector in selectors:
            name = response.css(selector).get()
            if name:
                # Clean up title text (remove site name, etc.)
                name = name.strip()
                if ' - ' in name:
                    name = name.split(' - ')[0].strip()
                return name
        return None

    def extract_developer_name(self, response):
        # Extract developer/author name
        selectors = [
            '.author-name::text',
            '.developer-name::text',
            '.seller-name::text',
            'a[href*="/users/"]::text',
            '.product-author::text'
        ]
        
        for selector in selectors:
            developer = response.css(selector).get()
            if developer:
                return developer.strip()
        return None

    def extract_developer_id(self, response):
        # Extract developer ID from profile link
        developer_link = response.css('a[href*="/users/"]::attr(href)').get()
        if developer_link:
            # Extract username from URL like "/en/users/vasiliy_strukov"
            id_match = re.search(r'/users/([^/?]+)', developer_link)
            if id_match:
                return id_match.group(1)
        return None

    def extract_current_version(self, response):
        # Extract current version
        selectors = [
            '*:contains("Version")::text',
            '.version::text',
            '.current-version::text'
        ]
        
        for selector in selectors:
            version_text = response.css(selector).get()
            if version_text and 'version' in version_text.lower():
                # Extract version number like "Version 11.2" -> "11.2"
                version_match = re.search(r'(\d+\.?\d*)', version_text)
                if version_match:
                    return version_match.group(1)
        return None

    def extract_last_update(self, response):
        # Extract last update date
        selectors = [
            '*:contains("Updated")::text',
            '*:contains("Last update")::text',
            '.last-update::text',
            '.updated-date::text'
        ]
        
        for selector in selectors:
            update_text = response.css(selector).get()
            if update_text:
                # Extract date in various formats
                date_match = re.search(r'(\d{4}-\d{2}-\d{2})', update_text)
                if date_match:
                    return date_match.group(1)
        return None

    def extract_publish_date(self, response):
        # Extract publish/creation date
        selectors = [
            '*:contains("Published")::text',
            '*:contains("Created")::text',
            '.publish-date::text',
            '.created-date::text'
        ]
        
        for selector in selectors:
            publish_text = response.css(selector).get()
            if publish_text:
                # Extract date in various formats
                date_match = re.search(r'(\d{4}-\d{2}-\d{2})', publish_text)
                if date_match:
                    return date_match.group(1)
        return None

    def extract_activation_count(self, response):
        # Extract activation count
        selectors = [
            '*:contains("Activations")::text',
            '*:contains("Downloads")::text',
            '.activation-count::text'
        ]
        
        for selector in selectors:
            activation_text = response.css(selector).get()
            if activation_text:
                # Extract number
                count_match = re.search(r'(\d+)', activation_text)
                if count_match:
                    return int(count_match.group(1))
        return None

    def extract_review_count(self, response):
        # Extract review count
        selectors = [
            '*:contains("Reviews")::text',
            '*:contains("review")::text',
            '.review-count::text',
            '.reviews-total::text'
        ]
        
        for selector in selectors:
            review_text = response.css(selector).get()
            if review_text:
                # Extract number like "808 reviews" -> 808
                count_match = re.search(r'(\d+)', review_text)
                if count_match:
                    return int(count_match.group(1))
        return None

    def extract_comments_count(self, response):
        # Extract comments count
        selectors = [
            '*:contains("Comments")::text',
            '*:contains("comment")::text',
            '.comments-count::text'
        ]
        
        for selector in selectors:
            comment_text = response.css(selector).get()
            if comment_text:
                # Extract number
                count_match = re.search(r'(\d+)', comment_text)
                if count_match:
                    return int(count_match.group(1))
        return None

    def extract_rating_overall(self, response):
        # Extract overall rating
        selectors = [
            '.rating-overall::text',
            '.overall-rating::text',
            '*[class*="rating"]::text'
        ]
        
        for selector in selectors:
            rating_text = response.css(selector).get()
            if rating_text:
                # Extract rating like "4.8" or "4.8/5"
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    try:
                        return float(rating_match.group(1))
                    except ValueError:
                        continue
        return None

    def extract_rating_description_quality(self, response):
        # Extract description quality rating
        return self._extract_specific_rating(response, 'description')

    def extract_rating_reliability(self, response):
        # Extract reliability rating
        return self._extract_specific_rating(response, 'reliability')

    def extract_rating_support(self, response):
        # Extract support rating
        return self._extract_specific_rating(response, 'support')

    def _extract_specific_rating(self, response, rating_type):
        # Helper method to extract specific rating types
        selectors = [
            f'*:contains("{rating_type}")::text',
            f'.rating-{rating_type}::text',
            f'*[class*="{rating_type}"]::text'
        ]
        
        for selector in selectors:
            rating_text = response.css(selector).get()
            if rating_text and rating_type.lower() in rating_text.lower():
                # Extract rating number
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    try:
                        return float(rating_match.group(1))
                    except ValueError:
                        continue
        return None
