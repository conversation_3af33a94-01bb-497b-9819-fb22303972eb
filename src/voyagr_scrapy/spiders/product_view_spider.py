import scrapy
from datetime import datetime
from voyagr_scrapy.items import ProductViewItem
import re


class ProductViewSpider(scrapy.Spider):
    name = 'product_view'
    allowed_domains = ['mql5.com']
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(ProductViewSpider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = ProductViewItem()
        item['url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        item['product_extraction_timestamp'] = datetime.now().strftime('%d/%m/%Y %H:%M:%S')

        # Extract product information from the detailed product page
        item['product_id'] = self.extract_product_id(response)
        item['product_name'] = self.extract_product_name(response)
        item['developer_name'] = self.extract_developer_name(response)
        item['developer_id'] = self.extract_developer_id(response)
        item['current_version'] = self.extract_current_version(response)
        item['last_update'] = self.extract_last_update(response)
        item['publish_date'] = self.extract_publish_date(response)
        item['activation_count'] = self.extract_activation_count(response)
        item['review_count'] = self.extract_review_count(response)
        item['comments_count'] = self.extract_comments_count(response)
        item['rating_overall'] = self.extract_rating_overall(response)
        item['rating_description_quality'] = self.extract_rating_description_quality(response)
        item['rating_reliability'] = self.extract_rating_reliability(response)
        item['rating_support'] = self.extract_rating_support(response)

        yield item

    def extract_product_id(self, response):
        # Extract product ID from URL like "/en/market/product/118805"
        url_match = re.search(r'/product/(\d+)', response.url)
        if url_match:
            return url_match.group(1)
        return None

    def extract_product_name(self, response):
        # Extract product name from page title or main heading
        # First try h1 tag
        name = response.css('h1::text').get()
        if name:
            return name.strip()

        # Try title tag and clean it
        title = response.css('title::text').get()
        if title:
            # Remove " | Buy Trading Robot..." part
            if ' | ' in title:
                name = title.split(' | ')[0].strip()
                return name

        return None

    def extract_developer_name(self, response):
        # Extract developer/author name from the breadcrumb or profile link
        # Look for the developer link in the breadcrumb or product info
        developer_link = response.css('a[href*="/users/"]::text').get()
        if developer_link:
            return developer_link.strip()

        # Fallback: look for text patterns that might contain developer name
        # The page shows "Bogdan Ion Puscasu" as a link
        return None

    def extract_developer_id(self, response):
        # Extract developer ID from profile link
        developer_link = response.css('a[href*="/users/"]::attr(href)').get()
        if developer_link:
            # Extract username from URL like "/en/users/weredeu"
            id_match = re.search(r'/users/([^/?]+)', developer_link)
            if id_match:
                return id_match.group(1)
        return None

    def extract_current_version(self, response):
        # Extract current version from text like "Version: 2.2"
        # Look for text containing "Version:"
        all_text = ' '.join(response.css('*::text').getall())
        version_match = re.search(r'Version:\s*(\d+\.?\d*)', all_text)
        if version_match:
            return version_match.group(1)
        return None

    def extract_last_update(self, response):
        # Extract last update date from text like "Updated: 12 June 2025"
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Updated: DD Month YYYY" pattern
        update_match = re.search(r'Updated:\s*(\d{1,2}\s+\w+\s+\d{4})', all_text)
        if update_match:
            # Convert to YYYY-MM-DD format
            date_str = update_match.group(1)
            try:
                from datetime import datetime
                # Parse date like "12 June 2025"
                parsed_date = datetime.strptime(date_str, '%d %B %Y')
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                return date_str  # Return original if parsing fails
        return None

    def extract_publish_date(self, response):
        # Extract publish date from text like "Published: 21 June 2024"
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Published: DD Month YYYY" pattern
        publish_match = re.search(r'Published:\s*(\d{1,2}\s+\w+\s+\d{4})', all_text)
        if publish_match:
            # Convert to YYYY-MM-DD format
            date_str = publish_match.group(1)
            try:
                from datetime import datetime
                # Parse date like "21 June 2024"
                parsed_date = datetime.strptime(date_str, '%d %B %Y')
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                return date_str  # Return original if parsing fails
        return None

    def extract_activation_count(self, response):
        # Extract activation count from text like "Activations: 10"
        all_text = ' '.join(response.css('*::text').getall())
        activation_match = re.search(r'Activations:\s*(\d+)', all_text)
        if activation_match:
            return int(activation_match.group(1))
        return None

    def extract_review_count(self, response):
        # Extract review count from text like "Reviews (153)" or tab text
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Reviews (153)" pattern
        review_match = re.search(r'Reviews\s*\((\d+)\)', all_text)
        if review_match:
            return int(review_match.group(1))
        return None

    def extract_comments_count(self, response):
        # Extract comments count from text like "Comments (203)" or tab text
        all_text = ' '.join(response.css('*::text').getall())
        # Look for "Comments (203)" pattern
        comment_match = re.search(r'Comments\s*\((\d+)\)', all_text)
        if comment_match:
            return int(comment_match.group(1))
        return None

    def extract_rating_overall(self, response):
        # Extract overall rating from text like "4.99"
        # Look for the rating number displayed prominently on the page
        all_text = ' '.join(response.css('*::text').getall())
        # Look for standalone rating numbers (like "4.99")
        rating_matches = re.findall(r'\b(\d+\.\d{2})\b', all_text)
        for rating in rating_matches:
            rating_val = float(rating)
            # Rating should be between 1 and 5
            if 1.0 <= rating_val <= 5.0:
                return rating_val
        return None

    def extract_rating_description_quality(self, response):
        # Extract description quality rating - not available on this page structure
        # MQL5 doesn't seem to show separate rating categories on product pages
        return None

    def extract_rating_reliability(self, response):
        # Extract reliability rating - not available on this page structure
        # MQL5 doesn't seem to show separate rating categories on product pages
        return None

    def extract_rating_support(self, response):
        # Extract support rating - not available on this page structure
        # MQL5 doesn't seem to show separate rating categories on product pages
        return None
