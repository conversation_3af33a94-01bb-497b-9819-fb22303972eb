import scrapy
from datetime import datetime
from voyagr_scrapy.items import UserItem


class UserSpider(scrapy.Spider):
    name = 'user'
    allowed_domains = []
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(User<PERSON>pider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = UserItem()
        item['url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        
        # Extract user information - customize based on target sites
        item['user_id'] = self.extract_user_id(response)
        item['username'] = self.extract_username(response)
        item['display_name'] = self.extract_display_name(response)
        item['profile_url'] = response.url
        item['avatar_url'] = self.extract_avatar_url(response)
        item['registration_date'] = self.extract_registration_date(response)
        item['last_active'] = self.extract_last_active(response)
        item['reputation_score'] = self.extract_reputation_score(response)
        item['total_reviews'] = self.extract_total_reviews(response)
        item['total_products'] = self.extract_total_products(response)
        item['location'] = self.extract_location(response)
        item['verified'] = self.extract_verified(response)
        item['account_type'] = self.extract_account_type(response)
        item['social_links'] = self.extract_social_links(response)
        item['contact_info'] = self.extract_contact_info(response)
        item['market_id'] = self.extract_market_id(response)
        
        yield item

    def extract_user_id(self, response):
        return response.css('::attr(data-user-id)').get() or response.url.split('/')[-1]

    def extract_username(self, response):
        return response.css('.username::text, .user-name::text, .handle::text').get()

    def extract_display_name(self, response):
        return response.css('.display-name::text, .full-name::text, h1::text').get()

    def extract_avatar_url(self, response):
        return response.css('.avatar img::attr(src), .profile-picture::attr(src)').get()

    def extract_registration_date(self, response):
        return response.css('.joined::text, .member-since::text, .registration-date::text').get()

    def extract_last_active(self, response):
        return response.css('.last-active::text, .last-seen::text').get()

    def extract_reputation_score(self, response):
        score_text = response.css('.reputation::text, .score::text, .points::text').get()
        if score_text:
            import re
            score_match = re.search(r'\d+', score_text)
            return int(score_match.group()) if score_match else None
        return None

    def extract_total_reviews(self, response):
        reviews_text = response.css('.total-reviews::text, .review-count::text').get()
        if reviews_text:
            import re
            reviews_match = re.search(r'\d+', reviews_text)
            return int(reviews_match.group()) if reviews_match else None
        return None

    def extract_total_products(self, response):
        products_text = response.css('.total-products::text, .product-count::text').get()
        if products_text:
            import re
            products_match = re.search(r'\d+', products_text)
            return int(products_match.group()) if products_match else None
        return None

    def extract_location(self, response):
        return response.css('.location::text, .address::text, .city::text').get()

    def extract_verified(self, response):
        verified_element = response.css('.verified, .badge-verified, .checkmark')
        return bool(verified_element) if verified_element else False

    def extract_account_type(self, response):
        return response.css('.account-type::text, .user-type::text, .membership::text').get()

    def extract_social_links(self, response):
        links = {}
        social_links = response.css('.social-links a, .social a')
        for link in social_links:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get() or link.css('::attr(title)').get()
            if href and text:
                links[text.strip()] = href
        return links

    def extract_contact_info(self, response):
        contact = {}
        email = response.css('.email::text, [href^="mailto:"]::attr(href)').get()
        phone = response.css('.phone::text, .tel::text').get()
        website = response.css('.website::attr(href), .personal-site::attr(href)').get()
        
        if email:
            contact['email'] = email.replace('mailto:', '')
        if phone:
            contact['phone'] = phone
        if website:
            contact['website'] = website
            
        return contact

    def extract_market_id(self, response):
        return response.css('::attr(data-market-id)').get() or 'unknown'