import scrapy
from datetime import datetime
from voyagr_scrapy.items import DeveloperItem


class Developer<PERSON>pider(scrapy.Spider):
    name = 'developer'
    allowed_domains = []
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = DeveloperItem()
        item['profile_url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        
        # Extract developer information - customize based on target sites
        item['developer_id'] = self.extract_developer_id(response)
        item['name'] = self.extract_name(response)
        item['company_name'] = self.extract_company_name(response)
        item['website'] = self.extract_website(response)
        item['description'] = self.extract_description(response)
        item['established_date'] = self.extract_established_date(response)
        item['location'] = self.extract_location(response)
        item['contact_info'] = self.extract_contact_info(response)
        item['social_links'] = self.extract_social_links(response)
        item['total_products'] = self.extract_total_products(response)
        item['product_categories'] = self.extract_product_categories(response)
        item['rating'] = self.extract_rating(response)
        item['reviews_count'] = self.extract_reviews_count(response)
        item['verified'] = self.extract_verified(response)
        item['certifications'] = self.extract_certifications(response)
        item['portfolio'] = self.extract_portfolio(response)
        item['market_id'] = self.extract_market_id(response)
        
        yield item

    def extract_developer_id(self, response):
        return response.css('::attr(data-developer-id)').get() or response.url.split('/')[-1]

    def extract_name(self, response):
        return response.css('h1::text, .developer-name::text, .name::text').get()

    def extract_company_name(self, response):
        return response.css('.company::text, .organization::text, .business-name::text').get()

    def extract_website(self, response):
        return response.css('.website::attr(href), .homepage::attr(href), [rel="external"]::attr(href)').get()

    def extract_description(self, response):
        return ' '.join(response.css('.description::text, .about::text, .bio::text').getall())

    def extract_established_date(self, response):
        return response.css('.established::text, .founded::text, .since::text').get()

    def extract_location(self, response):
        return response.css('.location::text, .address::text, .headquarters::text').get()

    def extract_contact_info(self, response):
        contact = {}
        email = response.css('.email::text, [href^="mailto:"]::attr(href)').get()
        phone = response.css('.phone::text, .tel::text, .contact-phone::text').get()
        address = response.css('.address::text, .physical-address::text').get()
        
        if email:
            contact['email'] = email.replace('mailto:', '')
        if phone:
            contact['phone'] = phone
        if address:
            contact['address'] = address
            
        return contact

    def extract_social_links(self, response):
        links = {}
        social_links = response.css('.social-links a, .social a, .contact-links a')
        for link in social_links:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get() or link.css('::attr(title)').get()
            if href and text:
                links[text.strip()] = href
        return links

    def extract_total_products(self, response):
        products_text = response.css('.total-products::text, .product-count::text, .apps-count::text').get()
        if products_text:
            import re
            products_match = re.search(r'\d+', products_text)
            return int(products_match.group()) if products_match else None
        return None

    def extract_product_categories(self, response):
        return response.css('.categories li::text, .specialties li::text, .skills li::text').getall()

    def extract_rating(self, response):
        rating_text = response.css('.rating::text, .score::text, .stars::attr(data-rating)').get()
        if rating_text:
            import re
            rating_match = re.search(r'[\d\.]+', rating_text)
            return float(rating_match.group()) if rating_match else None
        return None

    def extract_reviews_count(self, response):
        reviews_text = response.css('.reviews-count::text, .review-count::text, .testimonials-count::text').get()
        if reviews_text:
            import re
            reviews_match = re.search(r'\d+', reviews_text)
            return int(reviews_match.group()) if reviews_match else None
        return None

    def extract_verified(self, response):
        verified_element = response.css('.verified, .badge-verified, .checkmark, .trusted')
        return bool(verified_element) if verified_element else False

    def extract_certifications(self, response):
        return response.css('.certifications li::text, .badges li::text, .credentials li::text').getall()

    def extract_portfolio(self, response):
        portfolio = []
        portfolio_items = response.css('.portfolio-item, .project-item, .work-sample')
        for item in portfolio_items:
            project = {
                'title': item.css('.title::text, .name::text').get(),
                'description': item.css('.description::text, .summary::text').get(),
                'url': item.css('a::attr(href)').get(),
                'image': item.css('img::attr(src)').get()
            }
            if project.get('title'):
                portfolio.append(project)
        return portfolio

    def extract_market_id(self, response):
        return response.css('::attr(data-market-id)').get() or 'unknown'