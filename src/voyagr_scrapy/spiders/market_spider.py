import scrapy
from datetime import datetime
from voyagr_scrapy.items import ProductItem
import re


class MarketSpider(scrapy.Spider):
    name = 'market'
    allowed_domains = []
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(<PERSON>Spider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        # Find the products container using the specified XPath
        products_container = response.xpath('/html/body/div[1]/main/article/div[2]/div[4]/div[1]')

        if not products_container:
            self.logger.warning(f"Products container not found at specified XPath on {response.url}")
            return

        # Extract all product cards from the container
        product_cards = products_container.css('.product-card, [class*="product"]')

        if not product_cards:
            self.logger.warning(f"No product cards found in container on {response.url}")
            return

        self.logger.info(f"Found {len(product_cards)} products on {response.url}")

        # Process each product card
        for product_card in product_cards:
            item = ProductItem()
            item['url'] = response.url
            item['scraped_at'] = datetime.now().isoformat()

            # Extract product information from the card
            item['product_id'] = self.extract_product_id(product_card)
            item['name'] = self.extract_product_name(product_card)
            item['description'] = self.extract_product_description(product_card)
            item['price'] = self.extract_product_price(product_card)
            item['currency'] = self.extract_product_currency(product_card)
            item['category'] = self.extract_product_category(product_card)
            item['vendor'] = self.extract_product_author(product_card)
            item['rating'] = self.extract_product_rating(product_card)
            item['reviews_count'] = self.extract_product_reviews_count(product_card)
            item['images'] = self.extract_product_images(product_card)
            item['market_id'] = self.extract_market_id(response)

            yield item

    def extract_market_id(self, response):
        # Extract market ID from URL or page attributes
        return response.css('::attr(data-market-id)').get() or response.url.split('/')[-1]

    def extract_product_id(self, product_card):
        # Extract product ID from the product link
        product_link = product_card.css('.product-card__title::attr(href)').get()
        if product_link:
            # Extract ID from URL like "/en/market/product/118805"
            id_match = re.search(r'/product/(\d+)', product_link)
            if id_match:
                return id_match.group(1)
        return None

    def extract_product_name(self, product_card):
        # Extract product name from the title wrapper
        return product_card.css('.product-card__title-wrapper::text').get()

    def extract_product_description(self, product_card):
        # Extract product description
        description = product_card.css('.product-card__description::text').get()
        return description.strip() if description else None

    def extract_product_price(self, product_card):
        # Extract price from the price element
        price_text = product_card.css('.product-card__price::text').get()
        if price_text:
            # Extract numeric value from price text like "1 199.99"
            price_match = re.search(r'[\d\s,]+\.?\d*', price_text.replace(' ', '').replace(',', ''))
            if price_match:
                try:
                    return float(price_match.group().replace(' ', ''))
                except ValueError:
                    pass
        return None

    def extract_product_currency(self, product_card):
        # Extract currency from price element or data attribute
        currency = product_card.css('.product-card__price::attr(data-fz-unit)').get()
        if currency:
            return currency

        # Fallback: extract from price text
        price_text = product_card.css('.product-card__price::text').getall()
        for text in price_text:
            if 'USD' in text:
                return 'USD'
            elif 'EUR' in text:
                return 'EUR'
        return 'USD'  # Default fallback

    def extract_product_category(self, product_card):
        # Extract category from the category section
        return product_card.css('.product-card__category-name a::text').get()

    def extract_product_author(self, product_card):
        # Extract author/vendor name
        return product_card.css('.product-card__author::text').get()

    def extract_product_rating(self, product_card):
        # Extract rating from the rating info
        rating_text = product_card.css('.g-rating__info::text').get()
        if rating_text:
            # Extract rating like "4.99 (126)" -> 4.99
            rating_match = re.search(r'([\d\.]+)', rating_text)
            if rating_match:
                try:
                    return float(rating_match.group(1))
                except ValueError:
                    pass
        return None

    def extract_product_reviews_count(self, product_card):
        # Extract reviews count from rating info
        rating_text = product_card.css('.g-rating__info::text').get()
        if rating_text:
            # Extract count like "4.99 (126)" -> 126
            count_match = re.search(r'\((\d+)\)', rating_text)
            if count_match:
                try:
                    return int(count_match.group(1))
                except ValueError:
                    pass
        return None

    def extract_product_images(self, product_card):
        # Extract product logo/image
        image_src = product_card.css('.product-card__logo::attr(src)').get()
        return [image_src] if image_src else []
