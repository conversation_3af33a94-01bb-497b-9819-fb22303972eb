import scrapy
from datetime import datetime
from voyagr_scrapy.items import ProductItem
import re


class MarketSpider(scrapy.Spider):
    name = 'market'
    allowed_domains = []
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(<PERSON>Spider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        # Extract product links directly - more reliable approach
        product_links = response.css('a[href*="/market/product/"]')

        if not product_links:
            self.logger.warning(f"No product links found on {response.url}")
            return

        self.logger.info(f"Found {len(product_links)} product links on {response.url}")

        # Group product elements by their product ID to avoid duplicates
        processed_products = set()

        # Process each product link and find its parent container
        for product_link in product_links:
            # Extract product ID from the link
            product_url = product_link.css('::attr(href)').get()
            if not product_url:
                continue

            id_match = re.search(r'/product/(\d+)', product_url)
            if not id_match:
                continue

            product_id = id_match.group(1)
            if product_id in processed_products:
                continue

            processed_products.add(product_id)

            # Find the parent container that holds all product information
            # Look for the closest parent that contains product card elements
            try:
                product_card = product_link.xpath('./ancestor::*[descendant::img][1]')
                if not product_card:
                    # Fallback: use a broader parent container
                    product_card = product_link.xpath('./ancestor::*[position()<=5][1]')
            except Exception as e:
                self.logger.error(f"XPath error for product {product_id}: {e}")
                continue

            item = ProductItem()
            item['url'] = response.url
            item['scraped_at'] = datetime.now().isoformat()

            # Extract product information
            item['product_id'] = product_id
            item['name'] = self.extract_product_name_from_link(product_link)
            item['description'] = None  # Removed description extraction as requested
            item['price'] = self.extract_product_price_near_link(product_link)
            item['currency'] = self.extract_product_currency_near_link(product_link)
            item['category'] = self.extract_product_category_near_link(product_link)
            item['vendor'] = self.extract_product_author_near_link(product_link)
            item['rating'] = self.extract_product_rating_near_link(product_link)
            item['reviews_count'] = self.extract_product_reviews_count_near_link(product_link)
            item['images'] = self.extract_product_images_near_link(product_link)
            item['market_id'] = self.extract_market_id(response)

            # Debug logging
            self.logger.info(f"Extracted item for product {product_id}: name='{item['name']}', price={item['price']}")

            yield item

    def extract_market_id(self, response):
        # Extract market ID from URL or page attributes
        return response.css('::attr(data-market-id)').get() or response.url.split('/')[-1]

    def extract_product_name_from_link(self, product_link):
        # Extract product name from the link text or nearby elements
        # First try to get text from within the link
        name = product_link.css('::text').get()
        if name and name.strip():
            return name.strip()

        # Try to find name in child elements of the link
        name = product_link.css('.product-card__title-wrapper::text, span::text').get()
        if name and name.strip():
            return name.strip()

        # Try to get all text within the link
        all_text = ' '.join(product_link.css('*::text').getall()).strip()
        if all_text:
            return all_text

        # Fallback: extract from alt attribute of nearby images in parent container
        container = product_link.xpath('./ancestor::*[descendant::img][1]')
        if container:
            alt_text = container.css('img::attr(alt)').get()
            if alt_text and alt_text.strip():
                return alt_text.strip()

        return None



    def extract_product_price_near_link(self, product_link):
        # Find price near the product link using the specific structure you mentioned
        # CSS selector: div.product-card:nth-child(1) > div:nth-child(2) > div:nth-child(3) > a:nth-child(1)

        # First, find the product card container
        container = product_link.xpath('./ancestor::*[contains(@class, "product-card")][1]')
        if not container:
            # Fallback: look for any parent container
            container = product_link.xpath('./ancestor::*[position()<=5][1]')

        if container:
            # Try multiple approaches to find the price
            price_selectors = [
                # Based on your specific CSS selector structure
                'div:nth-child(2) div:nth-child(3) a',
                'div div:nth-child(3) a',
                # Look for elements with price-related classes
                '.product-card__price',
                'a[class*="price"]',
                '*[class*="price"]',
                # Look for links that might contain price (product links often have price data)
                'a[href*="/market/product/"]',
            ]

            for selector in price_selectors:
                elements = container.css(selector)
                for element in elements:
                    # Get text content
                    price_text = element.css('::text').get()
                    if price_text and price_text.strip():
                        # Check if this text contains price information
                        if self._looks_like_price(price_text):
                            price = self._extract_price_from_text(price_text)
                            if price is not None:
                                return price

                    # Also check data attributes that might contain price
                    price_data = element.css('::attr(data-fz-value)').get()
                    if price_data:
                        price = self._extract_price_from_text(price_data)
                        if price is not None:
                            return price

            # Fallback: scan all text in the container for price patterns
            all_text = ' '.join(container.css('*::text').getall())
            if all_text:
                # Look for patterns like "1 199.99 USD" or "1,199.99"
                price_patterns = [
                    r'([\d\s,]+\.?\d*)\s*(USD|EUR|GBP|\$|€|£)',  # Price with currency
                    r'([\d\s,]+\.?\d*)',  # Just numbers (if in price context)
                ]

                for pattern in price_patterns:
                    matches = re.findall(pattern, all_text, re.IGNORECASE)
                    for match in matches:
                        price_str = match[0] if isinstance(match, tuple) else match
                        price = self._extract_price_from_text(price_str)
                        if price is not None and price > 0:
                            return price

        return None

    def _looks_like_price(self, text):
        """Check if text looks like it contains price information"""
        if not text:
            return False
        # Check for currency symbols or codes
        currency_indicators = ['USD', 'EUR', 'GBP', '$', '€', '£', 'CAD', 'AUD']
        has_currency = any(curr in text.upper() for curr in currency_indicators)
        # Check for numeric patterns
        has_numbers = re.search(r'\d', text)
        return has_currency or (has_numbers and len(text.strip()) < 20)  # Short numeric text might be price

    def _extract_price_from_text(self, text):
        """Extract numeric price from text, handling various formats"""
        if not text:
            return None

        try:
            # Handle formats like "1 199.99", "1,199.99", "1199.99"
            # Remove currency symbols and extra characters
            clean_text = re.sub(r'[^\d\s,.]', '', text)

            # Find numeric pattern
            price_match = re.search(r'([\d\s,]+\.?\d*)', clean_text)
            if price_match:
                price_str = price_match.group(1).strip()

                # Handle thousands separators
                if ' ' in price_str:
                    # Format like "1 199.99" - spaces as thousands separators
                    price_str = price_str.replace(' ', '')
                elif ',' in price_str and '.' in price_str:
                    # Format like "1,199.99" - comma as thousands separator
                    price_str = price_str.replace(',', '')
                elif ',' in price_str and '.' not in price_str:
                    # Could be European format "1199,99" - comma as decimal separator
                    parts = price_str.split(',')
                    if len(parts) == 2 and len(parts[1]) <= 2:
                        price_str = parts[0] + '.' + parts[1]
                    else:
                        price_str = price_str.replace(',', '')

                return float(price_str)
        except (ValueError, AttributeError):
            pass

        return None

    def extract_product_currency_near_link(self, product_link):
        # Find currency near the product link
        # Look for data attribute first (most reliable)
        currency = product_link.css('::attr(data-fz-unit)').get()
        if currency:
            return currency

        # Look in parent container for price text
        container = product_link.xpath('./ancestor::*[contains(@class, "product-card")][1]')
        if container:
            # Get all text content and look for currency patterns
            all_text = ' '.join(container.css('*::text').getall())

            # Look for common currency patterns
            currency_patterns = [
                r'\b(USD|EUR|GBP|JPY|CAD|AUD|CHF)\b',  # Standard currency codes
                r'\$',  # Dollar symbol
                r'€',   # Euro symbol
                r'£',   # Pound symbol
            ]

            for pattern in currency_patterns:
                match = re.search(pattern, all_text, re.IGNORECASE)
                if match:
                    currency_found = match.group(1) if match.group(1) else match.group(0)
                    # Normalize currency symbols to codes
                    currency_map = {'$': 'USD', '€': 'EUR', '£': 'GBP'}
                    return currency_map.get(currency_found, currency_found.upper())

        return 'USD'  # Default fallback

    def extract_product_category_near_link(self, product_link):
        # Find category near the product link
        container = product_link.xpath('./ancestor::*[descendant::*[contains(@class, "category") or contains(text(), "Expert") or contains(text(), "Indicator")]][1]')
        if container:
            category = container.css('.product-card__category-name a::text, .category::text, a[href*="/expert"]::text, a[href*="/indicator"]::text').get()
            if category:
                return category.strip()
        return None

    def extract_product_author_near_link(self, product_link):
        # Find author/vendor near the product link
        container = product_link.xpath('./ancestor::*[descendant::*[contains(@class, "author")]][1]')
        if container:
            author = container.css('.product-card__author::text, .author::text').get()
            if author:
                return author.strip()
        return None

    def extract_product_rating_near_link(self, product_link):
        # Find rating near the product link
        container = product_link.xpath('./ancestor::*[contains(@class, "product-card")][1]')
        if container:
            # Look for rating in specific elements
            rating_selectors = [
                '.g-rating__info::text',
                '.rating::text',
                '*[class*="rating"]::text'
            ]

            for selector in rating_selectors:
                rating_text = container.css(selector).get()
                if rating_text and '(' in rating_text:
                    # Extract rating like "4.99 (126)" -> 4.99
                    rating_match = re.search(r'([\d\.]+)', rating_text)
                    if rating_match:
                        try:
                            return float(rating_match.group(1))
                        except ValueError:
                            continue
        return None

    def extract_product_reviews_count_near_link(self, product_link):
        # Find reviews count near the product link
        container = product_link.xpath('./ancestor::*[contains(@class, "product-card")][1]')
        if container:
            # Look for review count in specific elements
            review_selectors = [
                '.g-rating__info::text',
                '*[class*="rating"]::text'
            ]

            for selector in review_selectors:
                rating_text = container.css(selector).get()
                if rating_text and '(' in rating_text:
                    # Extract count like "4.99 (126)" -> 126
                    count_match = re.search(r'\((\d+)\)', rating_text)
                    if count_match:
                        try:
                            return int(count_match.group(1))
                        except ValueError:
                            continue
        return None

    def extract_product_images_near_link(self, product_link):
        # Find images near the product link
        container = product_link.xpath('./ancestor::*[descendant::img][1]')
        if container:
            image_src = container.css('img::attr(src), .product-card__logo::attr(src)').get()
            if image_src:
                return [image_src]
        return []
