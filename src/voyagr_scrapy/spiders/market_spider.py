import scrapy
from datetime import datetime
from voyagr_scrapy.items import MarketItem


class MarketSpider(scrapy.Spider):
    name = 'market'
    allowed_domains = []
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(MarketSpider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        item = MarketItem()
        item['url'] = response.url
        item['scraped_at'] = datetime.now().isoformat()
        
        # Extract market information - customize based on target sites
        item['market_id'] = self.extract_market_id(response)
        item['name'] = self.extract_name(response)
        item['description'] = self.extract_description(response)
        item['category'] = self.extract_category(response)
        item['location'] = self.extract_location(response)
        item['status'] = self.extract_status(response)
        item['created_date'] = self.extract_created_date(response)
        item['updated_date'] = self.extract_updated_date(response)
        
        yield item

    def extract_market_id(self, response):
        # Implement market ID extraction logic
        return response.css('::attr(data-market-id)').get() or response.url.split('/')[-1]

    def extract_name(self, response):
        return response.css('h1::text, .market-name::text, .title::text').get()

    def extract_description(self, response):
        return response.css('.description::text, .about::text, p::text').get()

    def extract_category(self, response):
        return response.css('.category::text, .type::text').get()

    def extract_location(self, response):
        return response.css('.location::text, .address::text').get()

    def extract_status(self, response):
        return response.css('.status::text, .state::text').get()

    def extract_created_date(self, response):
        return response.css('.created::text, .established::text').get()

    def extract_updated_date(self, response):
        return response.css('.updated::text, .modified::text').get()