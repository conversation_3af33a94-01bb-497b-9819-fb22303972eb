import scrapy
from datetime import datetime
from voyagr_scrapy.items import ProductItem
import re


class MarketSpider(scrapy.Spider):
    name = 'market'
    allowed_domains = []
    start_urls = []

    def __init__(self, *args, **kwargs):
        super(<PERSON>Spider, self).__init__(*args, **kwargs)
        self.start_urls = kwargs.get('start_urls', '').split(',') if kwargs.get('start_urls') else []
        if kwargs.get('allowed_domains'):
            self.allowed_domains = kwargs.get('allowed_domains').split(',')

    def parse(self, response):
        # Try the specified XPath first
        products_container = response.xpath('/html/body/div[1]/main/article/div[2]/div[4]/div[1]')

        # Fallback: look for any container with product links
        if not products_container:
            self.logger.info(f"Specified container not found, trying fallback selectors on {response.url}")
            products_container = response.css('main, article, .content, body')

        if not products_container:
            self.logger.warning(f"No suitable container found on {response.url}")
            return

        # Extract product links directly - more reliable approach
        product_links = response.css('a[href*="/market/product/"]')

        if not product_links:
            self.logger.warning(f"No product links found on {response.url}")
            return

        self.logger.info(f"Found {len(product_links)} product links on {response.url}")

        # Group product elements by their product ID to avoid duplicates
        processed_products = set()

        # Process each product link and find its parent container
        for product_link in product_links:
            # Extract product ID from the link
            product_url = product_link.css('::attr(href)').get()
            if not product_url:
                continue

            id_match = re.search(r'/product/(\d+)', product_url)
            if not id_match:
                continue

            product_id = id_match.group(1)
            if product_id in processed_products:
                continue

            processed_products.add(product_id)

            # Find the parent container that holds all product information
            # Look for the closest parent that contains both the link and other product elements
            product_card = product_link.xpath('./ancestor::*[descendant::img and descendant::*[contains(text(), "USD") or contains(text(), "EUR")]][1]').get()
            if not product_card:
                # Fallback: use a broader parent container
                product_card = product_link.xpath('./ancestor::*[position()<=3][1]').get()

            if product_card:
                # Convert back to selector for easier extraction
                product_card = response.css('body').xpath(f'.//*[@*="{product_card}" or contains(., "{product_id}")]').get()
                if product_card:
                    product_card = response.css('body')  # Use the whole response for extraction
            else:
                product_card = response.css('body')  # Use the whole response as fallback

            item = ProductItem()
            item['url'] = response.url
            item['scraped_at'] = datetime.now().isoformat()

            # Extract product information, passing the product_url for context
            item['product_id'] = product_id
            item['name'] = self.extract_product_name_from_link(product_link)
            item['description'] = self.extract_product_description_near_link(response, product_link)
            item['price'] = self.extract_product_price_near_link(response, product_link)
            item['currency'] = self.extract_product_currency_near_link(response, product_link)
            item['category'] = self.extract_product_category_near_link(response, product_link)
            item['vendor'] = self.extract_product_author_near_link(response, product_link)
            item['rating'] = self.extract_product_rating_near_link(response, product_link)
            item['reviews_count'] = self.extract_product_reviews_count_near_link(response, product_link)
            item['images'] = self.extract_product_images_near_link(response, product_link)
            item['market_id'] = self.extract_market_id(response)

            yield item

    def extract_market_id(self, response):
        # Extract market ID from URL or page attributes
        return response.css('::attr(data-market-id)').get() or response.url.split('/')[-1]

    def extract_product_name_from_link(self, product_link):
        # Extract product name from the link text or nearby elements
        name = product_link.css('::text').get()
        if name:
            return name.strip()

        # Try to find name in nearby elements
        name = product_link.css('.product-card__title-wrapper::text').get()
        if name:
            return name.strip()

        # Fallback: extract from alt attribute of nearby images
        alt_text = product_link.xpath('.//img/@alt').get()
        if alt_text:
            return alt_text.strip()

        return None

    def extract_product_description_near_link(self, response, product_link):
        # Find description near the product link
        product_url = product_link.css('::attr(href)').get()
        if not product_url:
            return None

        # Look for description in the same container as the link
        container = product_link.xpath('./ancestor::*[contains(@class, "product") or contains(., "USD") or contains(., "EUR")][1]')
        if container:
            description = container.css('.product-card__description::text, .description::text, p::text').get()
            if description:
                return description.strip()

        return None

    def extract_product_price_near_link(self, response, product_link):
        # Find price near the product link
        product_url = product_link.css('::attr(href)').get()
        if not product_url:
            return None

        # Look for price in the same container or nearby
        container = product_link.xpath('./ancestor::*[descendant::*[contains(text(), "USD") or contains(text(), "EUR")]][1]')
        if container:
            price_text = container.css('.product-card__price::text, *:contains("USD")::text, *:contains("EUR")::text').get()
            if price_text:
                # Extract numeric value
                price_match = re.search(r'([\d\s,]+\.?\d*)', price_text.replace(' ', '').replace(',', ''))
                if price_match:
                    try:
                        return float(price_match.group(1).replace(' ', ''))
                    except ValueError:
                        pass
        return None

    def extract_product_currency_near_link(self, response, product_link):
        # Find currency near the product link
        container = product_link.xpath('./ancestor::*[descendant::*[contains(text(), "USD") or contains(text(), "EUR")]][1]')
        if container:
            # Look for data attribute first
            currency = container.css('*::attr(data-fz-unit)').get()
            if currency:
                return currency

            # Look for currency in text
            text_content = ' '.join(container.css('*::text').getall())
            if 'USD' in text_content:
                return 'USD'
            elif 'EUR' in text_content:
                return 'EUR'

        return 'USD'  # Default fallback

    def extract_product_category_near_link(self, response, product_link):
        # Find category near the product link
        container = product_link.xpath('./ancestor::*[descendant::*[contains(@class, "category") or contains(text(), "Expert") or contains(text(), "Indicator")]][1]')
        if container:
            category = container.css('.product-card__category-name a::text, .category::text, a[href*="/expert"]::text, a[href*="/indicator"]::text').get()
            if category:
                return category.strip()
        return None

    def extract_product_author_near_link(self, response, product_link):
        # Find author/vendor near the product link
        container = product_link.xpath('./ancestor::*[descendant::*[contains(@class, "author")]][1]')
        if container:
            author = container.css('.product-card__author::text, .author::text').get()
            if author:
                return author.strip()
        return None

    def extract_product_rating_near_link(self, response, product_link):
        # Find rating near the product link
        container = product_link.xpath('./ancestor::*[descendant::*[contains(@class, "rating") or contains(text(), "(")]]/1]')
        if container:
            rating_text = container.css('.g-rating__info::text, .rating::text, *:contains("(")::text').get()
            if rating_text:
                # Extract rating like "4.99 (126)" -> 4.99
                rating_match = re.search(r'([\d\.]+)', rating_text)
                if rating_match:
                    try:
                        return float(rating_match.group(1))
                    except ValueError:
                        pass
        return None

    def extract_product_reviews_count_near_link(self, response, product_link):
        # Find reviews count near the product link
        container = product_link.xpath('./ancestor::*[descendant::*[contains(text(), "(")]]/1]')
        if container:
            rating_text = container.css('.g-rating__info::text, *:contains("(")::text').get()
            if rating_text:
                # Extract count like "4.99 (126)" -> 126
                count_match = re.search(r'\((\d+)\)', rating_text)
                if count_match:
                    try:
                        return int(count_match.group(1))
                    except ValueError:
                        pass
        return None

    def extract_product_images_near_link(self, response, product_link):
        # Find images near the product link
        container = product_link.xpath('./ancestor::*[descendant::img][1]')
        if container:
            image_src = container.css('img::attr(src), .product-card__logo::attr(src)').get()
            if image_src:
                return [image_src]
        return []
