2025-06-13 14:48:44 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 14:48:44 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 14:48:44 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 14:48:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 14:48:44 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 14:48:44 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 14:48:44 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 14:48:44 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 14:48:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 14:48:44 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 14:48:44 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 14:48:44 [scrapy.core.engine] INFO: Spider opened
2025-06-13 14:48:44 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 14:48:44 [market] INFO: Spider opened: market
2025-06-13 14:48:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 14:48:45 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 14:48:45 [scrapy.core.scraper] ERROR: Spider error processing <GET https://www.mql5.com/en/market/mt5> (referer: None)
Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 619, in xpath
    result = xpathev(
             ^^^^^^^^
  File "src/lxml/etree.pyx", line 1624, in lxml.etree._Element.xpath
  File "src/lxml/xpath.pxi", line 290, in lxml.etree.XPathElementEvaluator.__call__
  File "src/lxml/xpath.pxi", line 210, in lxml.etree._XPathEvaluatorBase._handle_result
lxml.etree.XPathEvalError: Invalid predicate

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/defer.py", line 343, in iter_errback
    yield next(it)
          ^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/middlewares.py", line 17, in process_spider_output
    for i in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/depth.py", line 59, in process_spider_output
    yield from super().process_spider_output(response, result, spider)
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 69, in parse
    product_card = response.css('body').xpath(f'.//*[@*="{product_card}" or contains(., "{product_id}")]').get()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 181, in xpath
    flatten([x.xpath(xpath, namespaces=namespaces, **kwargs) for x in self])
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 181, in <listcomp>
    flatten([x.xpath(xpath, namespaces=namespaces, **kwargs) for x in self])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 626, in xpath
    raise ValueError(f"XPath error: {exc} in {query}")
ValueError: XPath error: Invalid predicate in .//*[@*="<div class="product-card__wrapper">
    <div class="product-card__header">
      <img class="product-card__logo" src="https://c.mql5.com/31/1404/quantum-queen-mt5-logo-200x200-2891.png" alt="Quantum Queen MT5" loading="lazy">
      <a href="/en/market/product/118805?source=Site+Market+MT5+Rating006" target="_blank" data-fz-event="MQL5+Market+Product+Click" data-fz-unit="USD" data-fz-value="1 199.99" class="product-card__title">
        <span class="product-card__title-wrapper">Quantum Queen MT5</span>
      </a>
      <div class="product-card__info">
        <div class="product-card__author">Bogdan Ion Puscasu</div>
        <div class="product-card__rating">
          <div class="g-rating g-rating_sm g-rating_v50"></div>
          <span class="g-rating__info">4.99 (126)</span>
        </div>
        <div class="product-card__category">
          <i class="icons-market icons-market_expert"></i>
          <span class="product-card__category-name"><a href="/en/market/mt5/expert">Experts</a></span>
        </div>
      </div>
    </div>
        <div class="product-card__description">Hello, traders! I am Quantum Queen, the newest and a very powerful addition to the Quantum Family of Expert Advisors. My specialty? GOLD. Yes, I trade the XAUUSD pair with precision and confidence, bringing you unparalleled trading opportunities on the glittering gold market. I am here to prove that I am the most advanced Gold trading Expert Advisor ever created.

IMPORTANT! After the purchase please send me a private message to receive the installation manual and the setup instructions. 

Live</div>
    <div>
      <a href="/en/market/product/118805?source=Site+Market+MT5+Rating006" target="_blank" data-fz-event="MQL5+Market+Product+Click" data-fz-unit="USD" data-fz-value="1 199.99" class="product-card__price">
        1 199.99
        USD
      </a>
    </div>
  </div>" or contains(., "118805")]
2025-06-13 14:48:45 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 14:48:45 [market] INFO: Exported 0 items to output/market_20250613_144844.json
2025-06-13 14:48:45 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 312,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.274946,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 12, 48, 45, 240418, tzinfo=datetime.timezone.utc),
 'httpcache/firsthand': 1,
 'httpcache/miss': 1,
 'httpcache/store': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'items_per_minute': None,
 'log_count/ERROR': 1,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75825152,
 'memusage/startup': 75825152,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'spider_exceptions/ValueError': 1,
 'spider_exceptions/count': 1,
 'start_time': datetime.datetime(2025, 6, 13, 12, 48, 44, 965472, tzinfo=datetime.timezone.utc)}
2025-06-13 14:48:45 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 14:49:07 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 14:49:07 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 14:49:07 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 14:49:07 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 14:49:07 [asyncio] DEBUG: Using selector: EpollSelector
2025-06-13 14:49:07 [scrapy.utils.log] DEBUG: Using reactor: twisted.internet.asyncioreactor.AsyncioSelectorReactor
2025-06-13 14:49:07 [scrapy.utils.log] DEBUG: Using asyncio event loop: asyncio.unix_events._UnixSelectorEventLoop
2025-06-13 14:49:07 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 14:49:07 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 14:49:07 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 14:49:07 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 14:49:07 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 14:49:07 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 14:49:07 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 14:49:07 [scrapy.core.engine] INFO: Spider opened
2025-06-13 14:49:07 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 14:49:07 [scrapy.extensions.httpcache] DEBUG: Using filesystem cache storage in /home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/.scrapy/httpcache
2025-06-13 14:49:07 [market] INFO: Spider opened: market
2025-06-13 14:49:07 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 14:49:07 [scrapy.core.engine] DEBUG: Crawled (200) <GET https://www.mql5.com/en/market/mt5> (referer: None) ['cached']
2025-06-13 14:49:08 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 14:49:08 [scrapy.core.scraper] ERROR: Spider error processing <GET https://www.mql5.com/en/market/mt5> (referer: None)
Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 619, in xpath
    result = xpathev(
             ^^^^^^^^
  File "src/lxml/etree.pyx", line 1624, in lxml.etree._Element.xpath
  File "src/lxml/xpath.pxi", line 290, in lxml.etree.XPathElementEvaluator.__call__
  File "src/lxml/xpath.pxi", line 210, in lxml.etree._XPathEvaluatorBase._handle_result
lxml.etree.XPathEvalError: Invalid predicate

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/defer.py", line 343, in iter_errback
    yield next(it)
          ^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/middlewares.py", line 17, in process_spider_output
    for i in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/depth.py", line 59, in process_spider_output
    yield from super().process_spider_output(response, result, spider)
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 69, in parse
    product_card = response.css('body').xpath(f'.//*[@*="{product_card}" or contains(., "{product_id}")]').get()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 181, in xpath
    flatten([x.xpath(xpath, namespaces=namespaces, **kwargs) for x in self])
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 181, in <listcomp>
    flatten([x.xpath(xpath, namespaces=namespaces, **kwargs) for x in self])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 626, in xpath
    raise ValueError(f"XPath error: {exc} in {query}")
ValueError: XPath error: Invalid predicate in .//*[@*="<div class="product-card__wrapper">
    <div class="product-card__header">
      <img class="product-card__logo" src="https://c.mql5.com/31/1404/quantum-queen-mt5-logo-200x200-2891.png" alt="Quantum Queen MT5" loading="lazy">
      <a href="/en/market/product/118805?source=Site+Market+MT5+Rating006" target="_blank" data-fz-event="MQL5+Market+Product+Click" data-fz-unit="USD" data-fz-value="1 199.99" class="product-card__title">
        <span class="product-card__title-wrapper">Quantum Queen MT5</span>
      </a>
      <div class="product-card__info">
        <div class="product-card__author">Bogdan Ion Puscasu</div>
        <div class="product-card__rating">
          <div class="g-rating g-rating_sm g-rating_v50"></div>
          <span class="g-rating__info">4.99 (126)</span>
        </div>
        <div class="product-card__category">
          <i class="icons-market icons-market_expert"></i>
          <span class="product-card__category-name"><a href="/en/market/mt5/expert">Experts</a></span>
        </div>
      </div>
    </div>
        <div class="product-card__description">Hello, traders! I am Quantum Queen, the newest and a very powerful addition to the Quantum Family of Expert Advisors. My specialty? GOLD. Yes, I trade the XAUUSD pair with precision and confidence, bringing you unparalleled trading opportunities on the glittering gold market. I am here to prove that I am the most advanced Gold trading Expert Advisor ever created.

IMPORTANT! After the purchase please send me a private message to receive the installation manual and the setup instructions. 

Live</div>
    <div>
      <a href="/en/market/product/118805?source=Site+Market+MT5+Rating006" target="_blank" data-fz-event="MQL5+Market+Product+Click" data-fz-unit="USD" data-fz-value="1 199.99" class="product-card__price">
        1 199.99
        USD
      </a>
    </div>
  </div>" or contains(., "118805")]
2025-06-13 14:49:08 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 14:49:08 [market] INFO: Exported 0 items to output/market_20250613_144907.json
2025-06-13 14:49:08 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 306,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.162543,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 12, 49, 8, 67693, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'items_per_minute': None,
 'log_count/DEBUG': 5,
 'log_count/ERROR': 1,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75116544,
 'memusage/startup': 75116544,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'spider_exceptions/ValueError': 1,
 'spider_exceptions/count': 1,
 'start_time': datetime.datetime(2025, 6, 13, 12, 49, 7, 905150, tzinfo=datetime.timezone.utc)}
2025-06-13 14:49:08 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 14:50:54 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 14:50:54 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 14:50:54 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 14:50:54 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 14:50:54 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 14:50:54 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 14:50:54 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 14:50:54 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 14:50:54 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 14:50:54 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 14:50:54 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 14:50:54 [scrapy.core.engine] INFO: Spider opened
2025-06-13 14:50:54 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 14:50:54 [market] INFO: Spider opened: market
2025-06-13 14:50:54 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 14:50:55 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Hello, traders! I am Quantum Queen, the newest and a very '
                'powerful addition to the Quantum Family of Expert Advisors. '
                'My specialty? GOLD. Yes, I trade the XAUUSD pair with '
                'precision and confidence, bringing you unparalleled trading '
                'opportunities on the glittering gold market. I am here to '
                'prove that I am the most advanced Gold trading Expert Advisor '
                'ever created.\n'
                '\n'
                'IMPORTANT! After the purchase please send me a private '
                'message to receive the installation manual and the setup '
                'instructions. \n'
                '\n'
                'Live',
 'images': ['https://c.mql5.com/31/1404/quantum-queen-mt5-logo-200x200-2891.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '118805',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.058843',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': '1 copies left at $999 Next price $1099 Let me introduce you '
                'to an Expert Advisor, built on the foundation of my manual '
                'trading system — Algo Pumping .\xa0I seriously upgraded this '
                'strat, loaded it with key tweaks, filters, and tech hacks, '
                'and now I’m dropping a trading bot that: Crushes the markets '
                'with the advanced Algo Pumping Swing Trading algorithm, Slaps '
                'Stop Loss orders to protect your account, Perfectly fits both '
                '"Prop Firm Trading" and "Personal Trading", Trades clean '
                'without martingale or',
 'images': ['https://c.mql5.com/31/1367/swing-master-ea-logo-200x200-6523.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '137361',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.063613',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing \xa0 Quantum Emperor EA , the groundbreaking MQL5 '
                "expert advisor that's transforming the way you trade the "
                'prestigious GBPUSD pair! Developed by a team of experienced '
                'traders with trading experience of over 13 years. IMPORTANT! '
                'After the purchase please send me a private message to '
                'receive the installation manual and the setup instructions. '
                '***Buy Quantum Emperor EA and you could get Quantum StarMan '
                'for free !*** Ask in private for more details Live Signal V5: '
                'Click Here \n'
                '\n'
                'MT4 Version :',
 'images': ['https://c.mql5.com/31/1404/quantum-emperor-mt5-logo-200x200-3721.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '103452',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.065688',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Quantum Bitcoin EA : There is no such thing as impossible, '
                "it's only a matter of figuring out how to do it! \n"
                'Step into the future of Bitcoin trading with Quantum Bitcoin '
                'EA , the latest masterpiece from one of the top MQL5 sellers. '
                'Designed for traders who demand performance, precision, and '
                "stability, Quantum Bitcoin redefines what's possible in the "
                'volatile world of cryptocurrency. \n'
                'IMPORTANT! After the purchase please send me a private '
                'message to receive the installation manual and the setup i',
 'images': ['https://c.mql5.com/31/1404/quantum-bitcoin-ea-logo-200x200-3009.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '127013',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.067931',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'GBPUSD Commander – Structured Scalping on M30-(lowest risk in '
                'the world) GBPUSD Commander is an Expert Advisor designed '
                'specifically for GBP/USD on the 30-minute timeframe. It '
                'applies structured technical logic with fixed Stop Loss and '
                'Take Profit levels, adaptive lot sizing, and risk control '
                'built into each position. The EA avoids the use of '
                'martingale, grid, or hedge techniques and operates with '
                'clearly defined trade logic for consistent execution. '
                'Suitable for both newer traders and experienc',
 'images': ['https://c.mql5.com/31/1415/gbpusd-commander-logo-200x200-1108.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '137760',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.069955',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ibrahim Aljaref'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing my new Expert Advisor Beatrix Inventor, Beatrix '
                'Inventor EA uses the concept of following trends in '
                'conducting market analysis. Analyzing market trends with the '
                'main indicators Bollinger Band and Moving Average, when '
                'entering transactions, this EA also considers the Orderblock '
                'zone which makes the analysis more accurate. The algorithm '
                'used in developing this EA is a reliable algorithm both in '
                'entry and managing floating minus.\n'
                '\n'
                'This EA is designed to be used on the XAUUSD / GOLD pair',
 'images': ['https://c.mql5.com/31/1402/beatrix-inventor-mt5-logo-200x200-9075.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '130426',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.071841',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Azil Al Azizul'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing AIQ Version 2.3—The Evolution of Autonomous '
                "Trading Intelligence I'm proud to present AIQ (Autonomous "
                'Intelligence), the next generation of AI-powered trading '
                'technology. Version 2.3 now features AI Web Search and '
                'Enhanced APM (AI Position Management) - AIQ can search the '
                'internet in real-time for breaking news, political events, '
                'interest rates, and market sentiment, while the upgraded APM '
                'manages your Take Profit and Stop Loss like a professional '
                'trader in real-time. Building on th',
 'images': ['https://c.mql5.com/31/1399/aiq-logo-200x200-1013.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '134329',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.075144',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Live signal \n'
                '\n'
                'Find out more here: \xa0 '
                'https://www.mql5.com/en/users/prizmal/seller \n'
                'The strategy uses an averaging trading approach, relying on '
                'the Stochastic Oscillator and Bollinger Bands as the main '
                'indicators. It consistently implements dynamic take-profit '
                'and stop-loss levels for each trade. Optimization was '
                'conducted using 14 years of data (from 2010 to 2024) on the '
                'IC Markets server with a Standard account type. \n'
                '\n'
                'Recommendations: \n'
                'Currency Pair: AUDCAD Minimum Deposit: $500 USD Account: H',
 'images': ['https://c.mql5.com/31/1418/monic-logo-200x200-3269.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '125754',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.077345',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladimir Lekhovitser'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'It helps to calculate the risk per trade, the easy '
                'installation of a new order, order management with partial '
                'closing functions, trailing stop of 7 types and other useful '
                'functions. \n'
                'Additional materials and instructions\n'
                'Installation instructions - Application instructions - Trial '
                'version of the application for a demo account \n'
                'Line function - \xa0 shows on the chart the Opening line, '
                'Stop Loss, Take Profit. With this function it is easy to set '
                'a new order and see its additional characteristics bef',
 'images': ['https://c.mql5.com/31/1243/trade-assistant-mt5-logo-200x200-4863.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '23415',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.079407',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Evgeniy Kravchenko'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'FastWay EA is a smart and efficient automated trading system '
                'built on a powerful mean-reversion strategy. It focuses on '
                'trading correlated currency pairs like AUDCAD, AUDNZD, '
                'NZDCAD, and EURGBP , capitalizing on price returning to its '
                'average after strong directional moves.\n'
                'After purchase, please send a private message to receive full '
                'setup instructions. \n'
                'Live Signal:\xa0 CLICK HERE \n'
                '\n'
                'Post-launch offer: \xa0 Regular price is $1487 , but now '
                'FastWay EA is available at a discount — only $1287 for the n',
 'images': ['https://c.mql5.com/31/1402/fastway-ea-logo-200x200-7424.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '139787',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.081710',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'PAVEL UDOVICHENKO'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'IMPORTANT : This package will only be sold at current price '
                'for a very limited number of copies. \xa0\xa0 Price will go '
                'to 1499$ very fast \xa0\xa0 +100 Strategies included and more '
                'coming! \n'
                'BONUS : At 999$ or higher price --> choose 5 \xa0of my other '
                "EA's for free!\xa0 ALL SET FILES COMPLETE SETUP AND "
                'OPTIMIZATION GUIDE VIDEO GUIDE LIVE SIGNALS REVIEW (3rd '
                'party) \n'
                "Welcome to the ULTIMATE BREAKOUT SYSTEM! I'm pleased to "
                'present the Ultimate Breakout System, a sophisticated and '
                'proprietary Expert Advisor (EA) met',
 'images': ['https://c.mql5.com/31/1420/ultimate-breakout-system-logo-200x200-3928.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '133653',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.083632',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Profalgo Limited'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Burning Grid EA MT5 – Multi-Pair Grid Power with Adaptive '
                'Risk \n'
                '\n'
                'Trade up to 35 forex pairs simultaneously with intelligent '
                'strategy selection, flexible risk profiles, and dynamic '
                'drawdown control.\n'
                'Actual Price: $999.00 - increases by with next 15 purchases '
                '(Next Price: $1199, Final price: $1999) Contact me to receive '
                'a time-limited, fully functional trial version! \n'
                'Manual: '
                'https://magma-software.solutions/burning-grid/bgmanual-en.html '
                'Community : https://www.mql5.com/en/messages/0151274c579fdb0',
 'images': ['https://c.mql5.com/31/1416/burning-grid-logo-200x200-1710.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '135273',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.088347',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Magma Software Solutions UG'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AI Neuro Dynamics EA Adaptive Signal Architecture for XAU/USD '
                '| H1 AI Neuro Dynamics is more than just an Expert Advisor — '
                'it is a modular cognitive trading system built for precision '
                'and adaptability on the XAU/USD (Gold) pair. Designed for '
                'high-volatility environments, it fully complies with the '
                'performance and risk requirements of prop firm standards. '
                'Powered by a proprietary neuro-quantum decision architecture '
                ', the EA evaluates market structure in real time, dynamically '
                'adjusting its inter',
 'images': ['https://c.mql5.com/31/1376/ai-neuro-dynamics-mt5-logo-200x200-8040.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '134465',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.090481',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Peter Robert Grange'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Monitoring of real trading Advisor -\xa0 '
                'https://www.mql5.com/en/signals/2264971 My other '
                'products\xa0 \xa0 -\xa0 \xa0 \xa0 click here Keep in mind '
                'that the results on different brokers may differ, I recommend '
                'testing on your broker before using it\xa0(you can ask me for '
                'a list of recommended brokers in the PM). Read the blog post '
                'with the description of the adviser before starting work and '
                'if you have any additional questions, write to me in the PM. '
                'A fully automatic Expert Advisor that does not require '
                'additional',
 'images': ['https://c.mql5.com/31/1206/king-sniper-ea-logo-200x200-7179.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '124990',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.093421',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ivan Bebikov'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'We proudly present our cutting-edge robot, the\xa0 Big Forex '
                'Players EA \xa0designed to maximize your trading potential, '
                'minimize emotional trading, and make smarter decisions '
                'powered by cutting-edge technology. The whole system in this '
                'EA took us many months to build, and then we spent a lot of '
                'time testing it.\xa0This unique EA includes three distinct '
                'strategies that can be used independently or in together.\xa0'
                'The robot receives the positions of the\xa0 biggest\xa0'
                'Banks \xa0(positions are sent from our database t',
 'images': ['https://c.mql5.com/31/1042/big-forex-players-mt5-logo-200x200-3102.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '92199',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.101459',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing our advanced Scalping Forex Robot. The scalping '
                'algorithm is built to spot high-probability entry and exit '
                'points, ensuring that every trade is executed with the '
                'highest chance of success within the M1 timeframe . The best '
                'pair to use with the Scalping Robot is XAUUSD .This robot is '
                'perfect for traders who prefer the scalping method and want '
                'to take advantage of rapid price movements without having to '
                'manually monitor the charts. It is suitable for both '
                'beginners looking for an autom',
 'images': ['https://c.mql5.com/31/1243/scalping-robot-mt5-logo-200x200-4701.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '127704',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.103991',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': "9 copies left at $599 Next price $699 Hey traders, If you're "
                "looking for an EA that doesn't just fire off trades for the "
                'sake of activity, but actually follows a smart, battle-tested '
                'strategy — meet Scalper Investor EA. This is a multi-currency '
                'expert advisor already armed with a solid reversal strategy, '
                'and soon to be upgraded with a trend-following module. Ready '
                'to trade: The Reversal Strategy\n'
                'At launch, Scalper Investor EA comes fully loaded with a '
                'reversal system designed to catch pullbacks',
 'images': ['https://c.mql5.com/31/1395/scalper-investor-logo-200x200-6238.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '139303',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.106751',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'Each buyer of this indicator also receives the following for '
                'free:\n'
                'The custom utility "Bomber Utility", which automatically '
                'manages every trade, sets Stop Loss and Take Profit levels, '
                'and closes trades according to the rules of this strategy Set '
                'files for configuring the indicator for various assets Set '
                'files for configuring Bomber Utility in the following modes: '
                '"Minimum Risk", "Balanced Risk", and "Wait-and-See Strategy" '
                'A step-by-step video manual to help you quickly install, '
                'configure, and s',
 'images': ['https://c.mql5.com/31/1354/divergence-bomber-logo-200x200-2956.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '136033',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.108914',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Bonnitta EA \xa0is based on Pending Position strategy ( PPS ) '
                'and a very advanced secretive trading algorithm. The strategy '
                'of\xa0 Bonnitta EA \xa0is a combination of a secretive custom '
                'indicator, Trendlines, Support & Resistance levels ( Price '
                'Action ) and most important secretive trading algorithm '
                "mentioned above. DON'T BUY AN EA WITHOUT ANY REAL MONEY TEST "
                'OF MORE THAN 3 MONTHS, IT TOOK ME MORE THAN 100 WEEKS(MORE '
                'THAN 2 YEARS) TO TEST BONNITTA EA ON REAL MONEY AND SEE THE '
                'RESULT ON THE LINK BELOW. B',
 'images': ['https://c.mql5.com/31/825/bonnitta-ea-mt5-logo-200x200-9279.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '52212',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.114153',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ugochukwu Mobi'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'GoldenHour Expert Advisor\xa0 A precision scalping system for '
                'XAUUSD (Gold) that focuses on high-probability '
                'single-position trades. The EA executes 2-3 carefully '
                'selected trades per day during optimal market conditions, '
                'avoiding risky multi-position or martingale strategies. \n'
                'NEXT price 599$\xa0\xa0 " If the EA doesn\'t perform well in '
                'your backtest, please feel free to message me. I’ll be happy '
                'to help you set it up correctly and get the best possible '
                'results ." \n'
                'Trading Approach: - Single position tra',
 'images': ['https://c.mql5.com/31/1383/goldenhour-logo-200x200-9546.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '132932',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.116583',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Zaha Feiz'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The GBPUSD Robot MT5 is an advanced automated trading system '
                'meticulously designed for the specific dynamics of the \xa0 '
                'GBP/USD \xa0 currency pair. Utilizing advanced technical '
                'analysis, the robot assesses historical and real-time data '
                'to \xa0 identify potential trends , key support and '
                'resistance levels, and other relevant market signals specific '
                'to GBP/USD.\xa0 The Robot opens positions\xa0 every day,\xa0 '
                'from Monday to Friday, and\xa0 all positions are secured \xa0'
                'with Take Profit, Stop Loss, Trailing Stop, Break-E',
 'images': ['https://c.mql5.com/31/1200/gbpusd-robot-mt5-logo-200x200-8029.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '123850',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.118988',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Forex EA Trading Channel on MQL5:\xa0 Join my MQL5 channel to '
                'update the latest news from me.\xa0 My community of over '
                '14,000 members on MQL5 . \n'
                'ONLY 3 COPIES OUT OF 10 LEFT AT $399! After that, the price '
                'will be raised to $499.\n'
                '\n'
                '- REAL SIGNAL\xa0 Low Risk:\xa0 '
                'https://www.mql5.com/en/signals/2302784 \n'
                'IC Markets - High Risk: \xa0 '
                'https://www.mql5.com/en/signals/2310008 \n'
                'Full installation instructions for EA AI Gold Sniper to work '
                'properly are updated at \xa0 comment #3 \n'
                '\n'
                'AI Gold Sniper applies the latest GPT-4o',
 'images': ['https://c.mql5.com/31/1358/ai-gold-scalper-mt5-logo-200x200-8759.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '133197',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.121122',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ho Tuan Thang'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Neuron is a distinctive Expert Advisor that continues '
                'the Aura series of trading systems. By leveraging advanced '
                'Neural Networks and cutting-edge classic trading strategies, '
                'Aura Neuron offers an innovative approach with excellent '
                'potential performance. Fully automated, this Expert Advisor '
                'is designed to trade currency pairs such as XAUUSD (GOLD). It '
                'has demonstrated consistent stability across these pairs from '
                '1999 to 2023. The system avoids dangerous money management '
                'techniques, such as m',
 'images': ['https://c.mql5.com/31/1359/aura-neuron-mt5-logo-200x200-4436.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '123089',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.123401',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'By popular demand from my large Xpert community (15,000+ '
                'downloads) , I’ve developed a fully automated Expert Advisor '
                'for the highly volatile gold market (XAUUSD) – a powerful '
                'solution for traders looking to capitalize on breakouts, '
                'follow trends, and utilize multiple signals per week . '
                'GoldXpert is designed specifically for beginners and '
                'semi-professionals , offering precise analysis to optimize '
                'your trading strategy and targeting those who want to benefit '
                'from dynamic market movements . Built',
 'images': ['https://c.mql5.com/31/1392/goldxpert-mt5-logo-200x200-1249.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '138998',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.127217',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Steve Rosenstock'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Way To Stars is an automated trading system based on the '
                'classic night scalping logic, designed to capture short-term '
                'opportunities during the lowest volatility periods of the '
                'market. \n'
                'Nighttime trading tends to have lower noise and weaker '
                'trends, making it suitable for high-frequency and precise '
                'operations. This type of strategy has existed in the field of '
                'algorithmic trading for over two decades. Way To Stars '
                'inherits this mature framework and rebuilds its algorithm to '
                'fully adapt to current',
 'images': ['https://c.mql5.com/31/1093/way-to-stars-mt5-logo-200x200-9461.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '113589',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.129594',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Wei Tu'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Gold Phoenix GPT - The Ultimate AI Trading Tool for Gold '
                'Pairs Introducing Gold Phoenix GPT, the most thorough and '
                'honest implementation of AI for gold trading. Built on '
                'real-world performance, this EA is specifically designed for '
                'gold pairs, using a powerful breakout strategy on the M1 '
                'timeframe. Unlike many tools that perform well in backtests '
                'but fall short in live conditions, Gold Phoenix GPT excels '
                'where it matters most: in fast-moving live gold markets. '
                'Powered by advanced AI—including Cha',
 'images': ['https://c.mql5.com/31/1219/the-gold-phoenix-g-p-t-logo-200x200-9392.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '124335',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.131479',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Black Edition\xa0is a fully automated EA designed to '
                'trade\xa0GOLD\xa0only. Expert showed stable results on XAUUSD '
                'in 2011-2020 period. No dangerous methods of money management '
                'used, no martingale, no grid or scalp. Suitable for any '
                'broker conditions. EA trained with a\xa0multilayer '
                'perceptron\xa0Neural Network\xa0(MLP) is a class of\xa0'
                'feedforward\xa0artificial neural network\xa0(ANN). The term '
                'MLP is used ambiguously, sometimes loosely to\xa0any\xa0'
                'feedforward ANN, sometimes strictly to refer to networks '
                'composed of mult',
 'images': ['https://c.mql5.com/31/1386/aura-black-edition-mt5-logo-200x200-8408.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '64961',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.133217',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': '【突破性US30交易策略】 '
                '这款EA专为US30指数（道琼斯工业平均指数）量身定制，融合先进的时间管理与动态风控技术，为您打造前瞻性且精准高效的交易体验。 '
                '为不同经验水平的交易者提供简便高效的交易体验，并支持个性化设置，满足多样化需求 ，在这里都能找到符合自己需求的操作模式。 '
                '如需查看该策略的交易表现: Live account signal \n'
                '\n'
                '专属资产优化 ：专为US30设计，精准捕捉市场脉动，不适用于其他货币对或资产。 灵活时间框架 '
                '：适用于任意时间框架，随时随地灵活操作。 个性化止损与止盈 '
                '：根据您的风控需求，自定义设置止损、止盈及追踪止损功能，确保风险与收益达到最佳平衡。 动态仓位管理 '
                '：支持固定手数与动态手数分配，满足不同账户规模的需求，提升资金效率。 小资金友好 '
                '：即使是较小资金账户也可顺利运行，体验专业级交易。 平台兼容推荐 ：建议使用对冲账户模式与高流动性平台（例如IC '
                'Market），并推荐在H1时间框架运行 。',
 'images': ['https://c.mql5.com/31/1318/apex-flow-logo-200x200-9015.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '133492',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.135594',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dong Zhi Sun'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Venom Gold Pro: Automated XAUUSD M30 EA Venom Gold Pro is a '
                "fully automated Expert Advisor. It's built for XAUUSD (Gold) "
                'on the M30 timeframe . This system uses advanced, pure price '
                'action logic. It monitors market behavior and executes trades '
                'in real-time. No indicators or martingale methods are used. '
                'We focus on clean, rule-based execution. Venom Gold Pro '
                'offers four proven, high-win-rate strategies . You can select '
                'the style that best fits your trading approach. Key Features '
                'Automated Tradin',
 'images': ['https://c.mql5.com/31/1418/venom-gold-pro-logo-200x200-1559.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '135571',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.139780',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antoine Melhem'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Generate consistent returns with a Grok3 AI-assisted , '
                'risk-diversified and Bitcoin-boosted EA . RiskKILLER AI is a '
                'breakout scalping algorithm identifying key levels for '
                'potential high-volatility moves, selecting best risk-reward '
                'trades while diversifying risk on 5 assets. $399: 4 copies '
                'left\xa0 / 10 . After purchase, to get the API key and the '
                'User Manual, 1. post a comment asking for them 2. mail me '
                'directly (mail findable in the dedicated group - see below). '
                '[ Live Signal ] - [\xa0 Specs & Set-up',
 'images': ['https://c.mql5.com/31/1420/risk-killer-ai-mt5-logo-200x200-7662.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '139184',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.142215',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Christophe Pa Trouillas'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AlphaCore X The AlphaCore X EA is a cutting-edge trading '
                'system that masters the complexity of financial markets with '
                'a unique combination of AI-driven analyses and data-based '
                'algorithms. By integrating ChatGPT-o1 , the latest GPT-4.5 , '
                'advanced machine learning models, and a robust big data '
                'approach, AlphaCore X achieves a new level of precision, '
                'adaptability, and efficiency. This Expert Advisor impresses '
                'with its innovative strategy, seamless AI interaction, and '
                'comprehensive additional featu',
 'images': ['https://c.mql5.com/31/1396/alphacore-x-logo-200x200-2807.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '139373',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.144046',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Arseny Potyekhin'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Bitcoin Robot Grid MT5 is an intelligent trading system '
                'designed to automate BTCUSD trading using the grid trading '
                'strategy. This method takes advantage of market fluctuations '
                'by placing a structured series of buy and sell orders at '
                'predefined price levels. The robot continuously monitors '
                'market conditions and executes trades according to its preset '
                'parameters, allowing for consistent market engagement without '
                'the need for manual intervention. Bitcoin Robot Grid is the '
                'perfect solution for trad',
 'images': ['https://c.mql5.com/31/1256/bitcoin-robot-grid-mt5-logo-200x200-5077.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '128620',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.166686',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Golden Algo\xa0– The Ultimate AI-Powered Expert Advisor for '
                'Gold Traders Golden Algo Expert Advisor is a powerful trading '
                'system designed specifically for XAUUSD (Gold). It combines '
                'technical indicators with real-time market data—including the '
                'US Index and market sentiment—to generate precise trade '
                'signals. Each signal is then filtered through an advanced '
                'OpenAI-powered process to ensure only high-probability trades '
                'are executed. By blending technical analysis, fundamental '
                'insights, and artificial',
 'images': ['https://c.mql5.com/31/1329/golden-algo-logo-200x200-4853.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '131124',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.170539',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ramethara Vijayanathan'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'ScalpPrime \xa0 EA \xa0 \n'
                'Advanced Dual-Strategy Expert Advisor for Structured and '
                'Rule-Based Gold Scalping \xa0 ScalpPrime \xa0 EA is a '
                'precision-engineered Expert Advisor tailored for trading '
                'XAUUSD (Gold) using short-term. It is built on a '
                'professional-grade, rule-based framework that combines two '
                'complementary approaches: a Fibonacci retracement strategy '
                'for \xa0 identifying \xa0 high-probability price zones and a '
                'volume-based confirmation system for filtering market noise '
                'and validating trade entries. \xa0 Desi',
 'images': ['https://c.mql5.com/31/1395/scalpprime-logo-200x200-6661.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '138544',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.172801',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'DRT Circle'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Bitcoin Hash EA is a distinctive Expert Advisor that '
                'continues the Aura series of trading systems. By leveraging '
                'advanced Neural Networks and cutting-edge classic trading '
                'strategies, Aura BTC offers an innovative approach with '
                'excellent potential performance. Fully automated, this Expert '
                'Advisor is designed to trade currency pair BTCUSD (Bitcoin). '
                'It has demonstrated consistent stability across these pairs '
                'from 2017 to 2025. The system avoids dangerous money '
                'management techniques, such as m',
 'images': ['https://c.mql5.com/31/1347/aura-bitcoin-hash-logo-200x200-8237.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '135582',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.174898',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'EA Gold Stuff mt5\xa0is an Expert Advisor designed '
                'specifically for trading gold. The operation is based on '
                'opening orders using the \xa0Gold Stuff mt5 \xa0indicator, '
                'thus the EA works according to the "Trend Follow" strategy, '
                'which means following the trend. For Expert Advisor need '
                'hedge type account\xa0 Contact me immediately after the '
                'purchase to get personal bonus!\xa0 You can get a free copy '
                'of our Strong Support and Trend Scanner indicator, please pm. '
                'me! Settings\xa0 and manual \xa0 here\xa0 \n'
                'Please note that I',
 'images': ['https://c.mql5.com/31/1340/ea-gold-stuff-mt5-logo-200x200-7851.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '54468',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.179597',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vasiliy Strukov'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Automated Zone-Adaptive Impulse Logic and AI in an EA\n'
                'INFusion is a fully automated Expert Advisor that trades '
                'exclusively the gold market (XAU/USD). Designed for '
                'MetaTrader, the system uses current GPT models\xa0 GPT-o4 '
                'high to analyze price data in real time. The EA combines '
                'modern AI methods with a proprietary price logic, creating a '
                'trading approach that flexibly adapts to market behavior – '
                'without traditional indicators, without martingale, grid or '
                'news trading. INFusion \xa0 is aimed at users s',
 'images': ['https://c.mql5.com/31/1380/quantedgex-logo-200x200-8739.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '138379',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.182218',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Viktoriya Volgina'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Hello everyone, let me introduce myself:\n'
                '\n'
                'I am Quantum StarMan, the electrifying, freshest member of '
                'the Quantum EAs family. \n'
                '\n'
                "I'm a fully automated, multicurrency EA with the power to "
                'handle up to 5 dynamic pairs: AUDUSD, EURAUD, EURUSD, GBPUSD, '
                'and USDCAD . With the utmost precision and unwavering '
                "responsibility, I'll take your trading game to the next "
                "level. Here's the kicker: I don't rely on Martingale "
                'strategies. Instead, I utilize a sophisticated grid system '
                "that's designed for peak perfor",
 'images': ['https://c.mql5.com/31/1404/quantum-starman-logo-200x200-9143.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '107189',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.185261',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AI ZeroPoint Dynamics EA Cognitive Signal Architecture | '
                'Multi-Asset Precision Engine “Not an EA. Not a strategy. A '
                'living system of inference, adaptation, and execution.” BORN '
                'FROM THE ZERO POINT AI ZeroPoint Dynamics EA is not built — '
                'it is calibrated.\n'
                'Not coded — but architected to function as a real-time '
                'cognitive organism , responding to markets with a depth of '
                'reasoning that mirrors human decision-making — yet surpasses '
                'it in scale, consistency, and velocity. At the heart of '
                'ZeroPoint lie',
 'images': ['https://c.mql5.com/31/1383/ai-zeropoint-dynamics-mt5-logo-200x200-4072.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '138199',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.187405',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Peter Robert Grange'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The XG Gold Robot MT5 is specially designed for Gold. We '
                'decided to include this EA in our offering after extensive '
                'testing .\xa0XG Gold Robot and works perfectly with the '
                'XAUUSD, GOLD, XAUEUR pairs. XG Gold Robot has been created '
                'for all traders who like to Trade in Gold and includes '
                'additional a function that displays weekly Gold levels with '
                'the minimum and maximum displayed in the panel as well as on '
                'the chart, which will help you in manual trading. It’s a '
                'strategy based on Price Action, Cycle S',
 'images': ['https://c.mql5.com/31/1058/xg-gold-robot-mt5-logo-200x200-7278.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '92195',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.192159',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Welcome to Trade Manager MT5 - the ultimate risk management '
                'tool designed to make trading more intuitive, precise, and '
                "efficient. This is not just an order placement tool; it's a "
                'comprehensive solution for seamless trade planning, position '
                "management, and enhanced control over risk. Whether you're a "
                'beginner taking your first steps, an advanced trader, or a '
                'scalper needing rapid executions, Trade Manager MT5 adapts to '
                'your needs, offering flexibility across all markets, from '
                'forex and indices t',
 'images': ['https://c.mql5.com/31/405/forex-trade-manager-mt5-logo-200x200-4875.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '39150',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.194879',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'InvestSoft'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Eternal Engine is an advanced EA that integrates multiple '
                'indicators with grid and Martingale strategies. Its core '
                'feature is precise entry point control, enabling it to '
                'perform exceptionally well even in complex market '
                'environments. Eternal Engine EA offers numerous trading '
                'opportunities, is not sensitive to spreads, and ensures '
                'accurate execution of every trade through strict entry point '
                'management. The strategy has been proven in live trading, '
                'providing over a year of low-drawdown real-time s',
 'images': ['https://c.mql5.com/31/1008/eternal-engine-ea-mt5-logo-200x200-9599.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '109036',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.197554',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Wei Tu'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'PUMPING STATION – Your Personal All-inclusive strategy\n'
                'Introducing\xa0PUMPING STATION\xa0— a revolutionary Forex '
                'indicator that will transform your trading into an exciting '
                'and effective activity! This indicator is not just an '
                'assistant but a full-fledged trading system with powerful '
                'algorithms that will help you start trading more stable! When '
                'you purchase this product, you also get FOR FREE: Exclusive '
                'Set Files:\xa0For automatic setup and maximum performance. '
                'Step-by-step video manual:\xa0Learn how to tr',
 'images': ['https://c.mql5.com/31/1323/algo-pumping-logo-200x200-1904.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '134062',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.200480',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'BreakTrue AI: Major levels breakout / rebound trading system '
                'with AI assistance. AI is optional and is used in the way it '
                'is supposed to be. \n'
                'Finally, the Expert Advisor which uses AI in the right way! '
                'BreakTrue AI combines sophisticated built-in trading strategy '
                'based on major levels true and false breakouts, with the '
                'cutting-edge technology of OpenAI’s ChatGPT which servers as '
                'additional entry filter. This isn’t just another empty claim '
                '— BreakTrue AI provides a genuine, fully integrated AI so',
 'images': ['https://c.mql5.com/31/1408/breaktrue-ai-mt5-logo-200x200-7785.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '140377',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.205159',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Andrey Barinov'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The Bitcoin Robot MT5 is engineered to execute Bitcoin trades '
                'with unparalleled efficiency and precision . Developed by a '
                'team of experienced traders and developers, our Bitcoin Robot '
                'employs a sophisticated algorithmic approach (price action, '
                'trend as well as two personalized indicators) to analyze '
                'market and execute trades swiftly with M5 timeframe , '
                'ensuring that you never miss out on lucrative opportunities. '
                'No grid, no martingale, no hedging, EA only open one position '
                'at the same time. Bit',
 'images': ['https://c.mql5.com/31/1079/bitcoin-robot-mt5-logo-200x200-2796.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '114522',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.226321',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Trade Panel is a multifunctional trading assistant. The '
                'application contains more than 50 trading functions for '
                'manual trading and allows you to automate most trading '
                'operations. Instructions for installing the application | '
                'Instructions for the application | Trial version of the '
                'application for a demo account Trade. Allows you to perform '
                'trading operations in one click: Open pending orders and '
                'positions with automatic risk calculation. Open multiple '
                'orders and positions with one click. Open ord',
 'images': ['https://c.mql5.com/31/1369/tradepanel-mt5-logo-200x200-2148.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '35049',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.228818',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Alfiya Fazylova'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Venom US30 Scalp — Pure Precision for US30 Trading Venom US30 '
                'Scalp is a fully automated Expert Advisor built for US30 (Dow '
                'Jones Index) on the M30 timeframe . It runs on a proprietary '
                'mathematical engine — no indicators, no martingale, no grid — '
                'just clean, logic-based trading. SIGNAL : Ask for signal Core '
                'Features Trend-following strategy with multi-layer '
                'confirmations Default risk: 0.01 lot per $500 (adjustable) '
                'Internal controls to reduce risk exposure Simple '
                'plug-and-play setup, no optimiz',
 'images': ['https://c.mql5.com/31/1366/venom-us30-scalp-logo-200x200-5259.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '136145',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.231008',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antoine Melhem'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing the DS Gold Robot, your ultimate companion in '
                'navigating the intricate world of XAUUSD trading. Developed '
                'with precision and powered by cutting-edge algorithms, DS '
                'Gold is a forex robot meticulously crafted to optimize your '
                'trading performance with\xa0 XAUUSD pairs . With its advanced '
                'analytical capabilities,\xa0 DS Gold \xa0Robot \xa0 '
                'constantly monitors the gold market, identifying key trends , '
                'patterns, and price movements with lightning speed. The DS '
                'Gold Robot opens positions every day from',
 'images': ['https://c.mql5.com/31/1182/ds-gold-robot-mt5-logo-200x200-9360.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '122975',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.233348',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'EvoTrade: The First Self-Learning Trading System on the '
                'Market Allow me to introduce EvoTrade —a unique trading '
                'advisor built using cutting-edge technologies in computer '
                'vision and data analysis. It is the first self-learning '
                'trading system on the market, operating in real-time. '
                'EvoTrade analyzes market conditions, adapts strategies, and '
                'dynamically adjusts to changes, delivering exceptional '
                'precision in any environment. EvoTrade employs advanced '
                'neural networks, including Long Short-Term Memory',
 'images': ['https://c.mql5.com/31/1267/evotrade-ea-mt5-logo-200x200-5183.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '129661',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.235441',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dolores Martin Munoz'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Fully Automated EA based on Supply and Demand Principles. The '
                'first Supply and Demand EA that is offering Complete '
                'Automation . Now trading Becomes Effortless, offering full '
                'control over your trading strategy through a User-Friendly '
                'graphical Trading Panel. You get a Super High Quality '
                'Algorithmic Trading Software that covers all trading styles '
                'Manual, Semi-Auto and Full-Auto.\xa0Through various settings '
                'and customization options, every trader can create a strategy '
                'that fits their own needs and per',
 'images': ['https://c.mql5.com/31/1110/supply-demand-ea-probot-mt5-logo-200x200-5181.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '117023',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.238648',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Georgios Kalomoiropoulos'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'PROP FIRM READY! ( download SETFILE ) LAUNCH PROMO: Only a '
                'few copies left at current price! Final price: 990$ Get 1 EA '
                'for free (for 2 trade accounts) -> contact me after purchase '
                'Ultimate Combo Deal \xa0 -> \xa0 click here JOIN PUBLIC '
                'GROUP: \xa0 Click here \n'
                'Live Signal\n'
                '\n'
                'Welcome to the Gold Reaper! Build on the very succesfull '
                'Goldtrade Pro, this EA has been designed to run on multiple '
                'timeframes at the same time, and has the option to set the '
                'trade frequency from very conservative to extreme volatile',
 'images': ['https://c.mql5.com/31/1365/the-gold-reaper-mt5-logo-200x200-7714.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '111357',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.240843',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Profalgo Limited'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'SmartChoise EA – Neural Network–Powered Trading System for '
                'XAU/USD (Gold) on M1 Timeframe\xa0\n'
                '\n'
                'The user manual is available via the link in my profile '
                'page.\n'
                'This EA is built for long-term, controlled '
                'growth—understanding and aligning it with your risk tolerance '
                'is key to its success. Uses a neural network–based engine '
                'that continuously analyzes real-time market data to adapt '
                'trading strategies according to current market conditions. '
                'This approach helps optimize trade entries, improve risk '
                'control,',
 'images': ['https://c.mql5.com/31/1305/smartchoise-logo-200x200-7053.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '128606',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.243391',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Gabriel Costin Floricel'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aria Connector EA (MT5 to ChatGPT and more...) Public '
                'channel:\xa0 https://www.mql5.com/en/channels/binaryforexea \n'
                'Many EAs on the market claim to use artificial intelligence '
                'or "neural networks" when in reality they only run '
                'traditional logic or connect with unreliable sources. Aria '
                'Connector EA was created with a clear and transparent '
                'purpose: to directly connect your MT5 platform with OpenAI’s '
                'AI , with no middlemen or shady scripts. From its first '
                'version, Aria establishes a real connection wit',
 'images': ['https://c.mql5.com/31/1412/aria-connector-ea-logo-200x200-8566.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '140434',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.246083',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Martin Alejandro Bamonte'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Big sale 50% OFF! Price $750. Regular price $1499 All our '
                'signals are now available on myfxbook: \xa0 click here \xa0 '
                'Unique set files and all recommendations are provided free of '
                'charge. All future updates of the adviser are included in the '
                'price. After the purchase, contact me and I will help you '
                'install and configure the robot correctly. I will also share '
                'with you information on how to get a free VPS from a reliable '
                'broker. Gold is one of the riskiest instruments on the '
                'market. It requires precisi',
 'images': ['https://c.mql5.com/31/1394/prometheus-mt5-logo-200x200-1628.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '128417',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.248182',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Evgenii Aksenov'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'DeepAlgo GPX ML - Real-time Analysis by Intelligent AI '
                'Models \n'
                'When you buy an AI EA, you have no idea if the EA is actually '
                'using AI. With DeepAlgo GPX ML, there are actual API calls '
                'directly to DeepSeek for every single trade and you can see '
                'this reflected on your API dashboard in real time. The API '
                'keys are 100% owned by you and you can manage and use them '
                'however you like. There are multiple fail-safes in place in '
                'order to manage unexpected problems with the API in case the '
                'API server is eve',
 'images': ['https://c.mql5.com/31/1378/deepalgo-gpx-ml-logo-200x200-2368.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '136885',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.269254',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Connor Michael Woodson'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing Scalper Deriv: Elevating Your Scalping '
                'Experience. Important : Scalper Deriv is ONLY sold through '
                'mql5.com. Any other website is a scam and is not Scalper '
                'Deriv.\xa0 To download the updated configuration files and '
                'additional strategies, click here For the user guide, click '
                'here . Are you one of those traders who find their passion in '
                'scalping and want to make the most of your capital? Whether '
                'you have a balance of $20, $200, $2000, $20000, or even '
                '$200000 in your account, we have the p',
 'images': ['https://c.mql5.com/31/1230/scalper-deriv-logo-200x200-9680.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '104565',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.271475',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antonio Simon Del Vecchio'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing Version 8.2—A Revolutionary Leap in AI Trading '
                "Technology I'm proud to announce my most significant update "
                'yet: Version 8.2. This groundbreaking release introduces AI '
                'Position Management and AI Web Search, which dynamically '
                'modifies Take Profit and Stop Loss levels in real-time while '
                'searching the internet for breaking news, political events, '
                'interest rates, and market sentiment, ensuring optimal '
                'position management with priority handling across all '
                'symbols. Version 8.2 harnesses th',
 'images': ['https://c.mql5.com/31/1401/mean-machine-logo-200x200-6801.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '122619',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.273687',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Fusion of Tradition and Innovation—Ushering in a New Era of '
                'Intelligent Trading 【Product Essence】 This EA is specifically '
                'designed for international gold (XAUUSD), ingeniously '
                'combining classic quantitative trading models with modern '
                'intelligent analytical technologies. It deeply explores '
                'market structure and volatility characteristics to achieve '
                'robust and efficient capital growth. The strategy '
                'demonstrates a steadily rising equity curve and low drawdown '
                'in various market environments, making t',
 'images': ['https://c.mql5.com/31/1406/goldmasterfusion-mt5-logo-200x200-6749.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '140128',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.275634',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Chen Jia Qi'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Experience exceptionally fast trade copying with the \xa0 '
                'Local Trade Copier EA MT5 . With its easy 1-minute setup, '
                'this trade copier allows you to copy trades between multiple '
                'MetaTrader terminals on the same Windows computer or Windows '
                'VPS with lightning-fast copying speeds of under 0.5 seconds. '
                "Whether you're a beginner or a professional trader, the \xa0 "
                'Local Trade Copier EA MT5 \xa0 offers a wide range of options '
                "to customize it to your specific needs. It's the ultimate "
                'solution for anyone looking t',
 'images': ['https://c.mql5.com/31/946/local-trade-copier-ea-mt5-logo-200x200-6081.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '68951',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.277332',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Juvenille Emperor Limited'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing TrendX Gold Scalper – The Next Evolution in Gold '
                'Trading Automation LAUNCH PROMO:\xa0 Only 2 copies left at '
                'current price. Next price is $553 The price will increase by '
                '$100 with every 10 purchases\xa0 Final price: $1933 \n'
                'LIVE SIGNAL ($100K account) :\xa0 '
                'https://www.mql5.com/en/signals/2304986?source=Site+Profile+Seller '
                'LIVE SIGNAL SMALL:\xa0 '
                'https://www.mql5.com/en/signals/2307472?source=Site+Profile+Seller '
                'LIVE COMBO TrendX + US30 Scalper + EURUSD Algo:\xa0 '
                'https://www.mql5.com/en/signals/23019',
 'images': ['https://c.mql5.com/31/1384/trendx-gold-scalper-logo-200x200-7715.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '138632',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.278961',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Lo Thi Mai Loan'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'HFT PropFirm EA MT5 is\xa0 also known as Green Man due to its '
                'distinctive logo by Dilwyn Tng, is an Expert Advisor (EA) '
                'crafted specifically for overcoming challenges or evaluations '
                'from proprietary trading firms (prop firms) that permit '
                'High-Frequency Trading (HFT) strategies.\n'
                '\n'
                'Now Greenman\xa0 HFT PropFirm EA MT5 is fully automatic! '
                'Free\xa0 1 All-In-One Breakout EA account licence with '
                'purchase of HFT PropFirm EA MT5 \n'
                'Passing HFT MT5 Challenge Performance Monitor: \n'
                'Broker: Fusion Market\n'
                'Login:\xa0\xa0172147',
 'images': ['https://c.mql5.com/31/1114/hft-propfirm-ea-mt5-logo-200x200-8392.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '117386',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.283272',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dilwyn Tng'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'No Stress FX — When Trading Feels a Bit Calmer When I started '
                'building No Stress FX, I wasn’t trying to chase miracles.\n'
                'The goal was to create something that fits a slower, more '
                'mindful style —\n'
                'without pressure, without noise, and hopefully with more '
                'clarity. It’s the kind of trading approach that simply makes '
                'more sense to me. Just being transparent Live Signal:\xa0 '
                'https://www.mql5.com/en/signals/2313926 \n'
                'Yes, it’s running on a real account.\n'
                'Some traders have found it interesting, and activity is',
 'images': ['https://c.mql5.com/31/1407/ai-no-stress-fx-mt5-logo-200x200-9829.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '139996',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.285527',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Mariia Aborkina'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': '3 copies left at $199\n'
                'Next price $299 Unique trading advisor for EURUSD\n'
                'The advisor is a modular trading system. It is based on an '
                'architecture in which each trading decision is formed not by '
                'a monolithic algorithm, but as a result of the interaction of '
                'independent logical blocks - indicator filters, entry '
                'conditions, exits and control rules. IMPORTANT! After '
                'purchase, send me a private message to receive the '
                'installation guide and setup instructions. Live signal '
                'Strategy XAUUSD - https://www.m',
 'images': ['https://c.mql5.com/31/1335/pure-ai-logo-200x200-4104.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '134947',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.341568',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vitali Vasilenka'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Live signal \n'
                '\n'
                'Find out more here: \xa0 '
                'https://www.mql5.com/en/users/prizmal/seller \n'
                'PrizmaL Scalper - Intraday Scalping for XAUUSD \n'
                'This trading algorithm is designed for speculative trading in '
                'the spot gold market XAUUSD.\n'
                'It employs advanced market microstructure analysis '
                'techniques, reacting to price impulses and liquidity in real '
                'time. The algorithm is not subject to swaps, making it '
                'particularly effective for active intraday trading.\n'
                'Optimized risk management and dynamic adaptation to volatil',
 'images': ['https://c.mql5.com/31/1388/prizmal-scalper-logo-200x200-7045.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '135294',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.361753',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladimir Lekhovitser'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'Unlock the Power of Trends Trading with the Trend Screener '
                'Indicator: Your Ultimate Trend Trading Solution powered by '
                'Fuzzy Logic and Multi-Currencies System! Elevate your trading '
                'game with the Trend Screener, the revolutionary trend '
                'indicator designed to transform your Metatrader into a '
                'powerful Trend Analyzer. This comprehensive tool leverages '
                'fuzzy logic and integrates over 13 premium features and three '
                'trading strategies, offering unmatched precision and '
                'versatility. LIMITED TIME OFFER : Tre',
 'images': ['https://c.mql5.com/31/1414/trend-screener-pro-mt5-logo-200x200-7413.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '47785',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.364217',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'STE S.S.COMPANY'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Our team is thrilled to introduce Trading Robot, the '
                'cutting-edge Smart Trading Expert Advisor for the MetaTrader '
                'terminal. AI Sniper is an intelligent, self-optimizing '
                'trading robot designed for MT5 . It leverages a smart '
                'algorithm and advanced trading strategies to maximize your '
                'trading potential. With 15 years of experience in trading '
                'exchanges and the stock market, we have developed innovative '
                'strategy management features, additional intelligent '
                'functions, and a user-friendly graphical inte',
 'images': ['https://c.mql5.com/31/1268/exp5-ai-sniper-for-mt5-logo-200x200-7487.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '118127',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.366587',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladislav Andruschenko'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Spiral Ascend EA – Smart Automated Trading for XAUUSD \n'
                'The\xa0 Spiral Ascend \xa0 EA is a powerful, fully automated '
                'trading solution tailored for the XAUUSD (Gold) market , '
                'combining classical Fibonacci methodologies with modern '
                'technical analysis and advanced volume-based logic. Developed '
                'with flexibility and precision in mind, this EA is ideal for '
                'traders seeking a disciplined and logic-driven approach to '
                'algorithmic trading—including those working under the '
                'conditions of prop trading firms . Buy S',
 'images': ['https://c.mql5.com/31/1368/spiral-ascend-logo-200x200-4715.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '136587',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.368817',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'DRT Circle'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'EASY Insight AIO – All-in-One Market Scanner with Full '
                'Indicator Data What if you could analyze the entire Forex '
                'market in seconds – without plotting anything on your '
                'charts? \n'
                'EASY Insight AIO integrates the full analytical output from '
                'FX Power, FX Volume, FX Dynamic, and FX Levels – into a '
                'single, structured CSV file. No extra tools needed. No chart '
                'overlays. No distractions. \n'
                '⸻ \n'
                'Why Use EASY Insight AIO? All Indicators Included: No need '
                'for additional licenses – AIO comes with full access to',
 'images': ['https://c.mql5.com/31/1384/easyinsight-aio-mt5-logo-200x200-8116.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '138518',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.371312',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Alain Verleyen'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'After 6 Years of Successful Manual Trading, My Strategies Are '
                'Now Available as Expert Advisors! \n'
                'Introducing the DAX Killer EA – a trading system built for '
                'the DAX Index from years of hands-on experience, extensive '
                'testing, and a steadfast commitment to secure, strategic '
                'trading. NO GRID, NO MARTINGALE, TIGHT SL EVERY TRADE. ONE '
                'TRADE PER DAY . \xa0 NO LOT MULTIPLIER. \xa0The price of the '
                'EA will increase by $100 with every 10 purchases. ICTRADING '
                'LIVE SIGNAL \xa0 DAX Killer Public \xa0 Chat \xa0 Group \xa0 '
                'IMPOR',
 'images': ['https://c.mql5.com/31/1337/dax-killer-logo-200x200-2452.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '134248',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.374626',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Pablo Dominguez Sanchez'}
2025-06-13 14:50:55 [scrapy.core.scraper] WARNING: Dropped: Missing required field: name in market spider
{'category': 'Experts',
 'currency': 'USD',
 'description': 'LAUNCH PROMO: Final price: 1,700$ Only 2 copies left at $399. '
                'Next price will be $499 Get 1 EA for free (for 2 trade '
                'accounts) -> contact me after purchase Instruction Blog Link '
                'to Channel \n'
                'Welcome to ZenFlow! ZenFlow is an advanced EA designed to '
                'adapt to changing market trends with precision and speed. It '
                'is optimized to trade the XAUUSD( or GOLD) symbol and should '
                'be run on only one chart. This EA uses a sophisticated '
                'trend-following strategy combined with a momentum-based '
                'indicator that ide',
 'images': ['https://c.mql5.com/31/1318/zen-flow-2-logo-200x200-9786.png'],
 'market_id': 'mt5',
 'name': '',
 'price': None,
 'product_id': '133718',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:50:55.377253',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Hamza Ashraf'}
2025-06-13 14:50:55 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 14:50:55 [market] INFO: Exported 0 items to output/market_20250613_145054.json
2025-06-13 14:50:55 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 310,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.477659,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 12, 50, 55, 384989, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'item_dropped_count': 70,
 'item_dropped_reasons_count/DropItem': 70,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 74,
 'memusage/max': 75309056,
 'memusage/startup': 75309056,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 12, 50, 54, 907330, tzinfo=datetime.timezone.utc)}
2025-06-13 14:50:55 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 14:52:29 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 14:52:29 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 14:52:29 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 14:52:29 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 14:52:29 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 14:52:29 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 14:52:29 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 14:52:29 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 14:52:29 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 14:52:29 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 14:52:29 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 14:52:29 [scrapy.core.engine] INFO: Spider opened
2025-06-13 14:52:29 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 14:52:29 [market] INFO: Spider opened: market
2025-06-13 14:52:29 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 14:52:29 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '1 copies left at $999 Next price $1099 Let me introduce you '
                'to an Expert Advisor, built on the foundation of my manual '
                'trading system — Algo Pumping .\xa0I seriously upgraded this '
                'strat, loaded it with key tweaks, filters, and tech hacks, '
                'and now I’m dropping a trading bot that: Crushes the markets '
                'with the advanced Algo Pumping Swing Trading algorithm, Slaps '
                'Stop Loss orders to protect your account, Perfectly fits both '
                '"Prop Firm Trading" and "Personal Trading", Trades clean '
                'without martingale or',
 'images': ['https://c.mql5.com/31/1367/swing-master-ea-logo-200x200-6523.png'],
 'market_id': 'mt5',
 'name': 'Swing Master EA',
 'price': None,
 'product_id': '137361',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.693701',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing \xa0 Quantum Emperor EA , the groundbreaking MQL5 '
                "expert advisor that's transforming the way you trade the "
                'prestigious GBPUSD pair! Developed by a team of experienced '
                'traders with trading experience of over 13 years. IMPORTANT! '
                'After the purchase please send me a private message to '
                'receive the installation manual and the setup instructions. '
                '***Buy Quantum Emperor EA and you could get Quantum StarMan '
                'for free !*** Ask in private for more details Live Signal V5: '
                'Click Here \n'
                '\n'
                'MT4 Version :',
 'images': ['https://c.mql5.com/31/1404/quantum-emperor-mt5-logo-200x200-3721.png'],
 'market_id': 'mt5',
 'name': 'Quantum Emperor MT5',
 'price': None,
 'product_id': '103452',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.697014',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Quantum Bitcoin EA : There is no such thing as impossible, '
                "it's only a matter of figuring out how to do it! \n"
                'Step into the future of Bitcoin trading with Quantum Bitcoin '
                'EA , the latest masterpiece from one of the top MQL5 sellers. '
                'Designed for traders who demand performance, precision, and '
                "stability, Quantum Bitcoin redefines what's possible in the "
                'volatile world of cryptocurrency. \n'
                'IMPORTANT! After the purchase please send me a private '
                'message to receive the installation manual and the setup i',
 'images': ['https://c.mql5.com/31/1404/quantum-bitcoin-ea-logo-200x200-3009.png'],
 'market_id': 'mt5',
 'name': 'Quantum Bitcoin EA',
 'price': None,
 'product_id': '127013',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.700052',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'GBPUSD Commander – Structured Scalping on M30-(lowest risk in '
                'the world) GBPUSD Commander is an Expert Advisor designed '
                'specifically for GBP/USD on the 30-minute timeframe. It '
                'applies structured technical logic with fixed Stop Loss and '
                'Take Profit levels, adaptive lot sizing, and risk control '
                'built into each position. The EA avoids the use of '
                'martingale, grid, or hedge techniques and operates with '
                'clearly defined trade logic for consistent execution. '
                'Suitable for both newer traders and experienc',
 'images': ['https://c.mql5.com/31/1415/gbpusd-commander-logo-200x200-1108.png'],
 'market_id': 'mt5',
 'name': 'GbpUsd Commander',
 'price': None,
 'product_id': '137760',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.702233',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ibrahim Aljaref'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing my new Expert Advisor Beatrix Inventor, Beatrix '
                'Inventor EA uses the concept of following trends in '
                'conducting market analysis. Analyzing market trends with the '
                'main indicators Bollinger Band and Moving Average, when '
                'entering transactions, this EA also considers the Orderblock '
                'zone which makes the analysis more accurate. The algorithm '
                'used in developing this EA is a reliable algorithm both in '
                'entry and managing floating minus.\n'
                '\n'
                'This EA is designed to be used on the XAUUSD / GOLD pair',
 'images': ['https://c.mql5.com/31/1402/beatrix-inventor-mt5-logo-200x200-9075.png'],
 'market_id': 'mt5',
 'name': 'Beatrix Inventor MT5',
 'price': None,
 'product_id': '130426',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.706698',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Azil Al Azizul'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing AIQ Version 2.3—The Evolution of Autonomous '
                "Trading Intelligence I'm proud to present AIQ (Autonomous "
                'Intelligence), the next generation of AI-powered trading '
                'technology. Version 2.3 now features AI Web Search and '
                'Enhanced APM (AI Position Management) - AIQ can search the '
                'internet in real-time for breaking news, political events, '
                'interest rates, and market sentiment, while the upgraded APM '
                'manages your Take Profit and Stop Loss like a professional '
                'trader in real-time. Building on th',
 'images': ['https://c.mql5.com/31/1399/aiq-logo-200x200-1013.png'],
 'market_id': 'mt5',
 'name': 'AiQ',
 'price': None,
 'product_id': '134329',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.710011',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Live signal \n'
                '\n'
                'Find out more here: \xa0 '
                'https://www.mql5.com/en/users/prizmal/seller \n'
                'The strategy uses an averaging trading approach, relying on '
                'the Stochastic Oscillator and Bollinger Bands as the main '
                'indicators. It consistently implements dynamic take-profit '
                'and stop-loss levels for each trade. Optimization was '
                'conducted using 14 years of data (from 2010 to 2024) on the '
                'IC Markets server with a Standard account type. \n'
                '\n'
                'Recommendations: \n'
                'Currency Pair: AUDCAD Minimum Deposit: $500 USD Account: H',
 'images': ['https://c.mql5.com/31/1418/monic-logo-200x200-3269.png'],
 'market_id': 'mt5',
 'name': 'Monic',
 'price': None,
 'product_id': '125754',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.713133',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladimir Lekhovitser'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'It helps to calculate the risk per trade, the easy '
                'installation of a new order, order management with partial '
                'closing functions, trailing stop of 7 types and other useful '
                'functions. \n'
                'Additional materials and instructions\n'
                'Installation instructions - Application instructions - Trial '
                'version of the application for a demo account \n'
                'Line function - \xa0 shows on the chart the Opening line, '
                'Stop Loss, Take Profit. With this function it is easy to set '
                'a new order and see its additional characteristics bef',
 'images': ['https://c.mql5.com/31/1243/trade-assistant-mt5-logo-200x200-4863.png'],
 'market_id': 'mt5',
 'name': 'Trade Assistant MT5',
 'price': None,
 'product_id': '23415',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.716048',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Evgeniy Kravchenko'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'FastWay EA is a smart and efficient automated trading system '
                'built on a powerful mean-reversion strategy. It focuses on '
                'trading correlated currency pairs like AUDCAD, AUDNZD, '
                'NZDCAD, and EURGBP , capitalizing on price returning to its '
                'average after strong directional moves.\n'
                'After purchase, please send a private message to receive full '
                'setup instructions. \n'
                'Live Signal:\xa0 CLICK HERE \n'
                '\n'
                'Post-launch offer: \xa0 Regular price is $1487 , but now '
                'FastWay EA is available at a discount — only $1287 for the n',
 'images': ['https://c.mql5.com/31/1402/fastway-ea-logo-200x200-7424.png'],
 'market_id': 'mt5',
 'name': 'FastWay EA',
 'price': None,
 'product_id': '139787',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.723826',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'PAVEL UDOVICHENKO'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'IMPORTANT : This package will only be sold at current price '
                'for a very limited number of copies. \xa0\xa0 Price will go '
                'to 1499$ very fast \xa0\xa0 +100 Strategies included and more '
                'coming! \n'
                'BONUS : At 999$ or higher price --> choose 5 \xa0of my other '
                "EA's for free!\xa0 ALL SET FILES COMPLETE SETUP AND "
                'OPTIMIZATION GUIDE VIDEO GUIDE LIVE SIGNALS REVIEW (3rd '
                'party) \n'
                "Welcome to the ULTIMATE BREAKOUT SYSTEM! I'm pleased to "
                'present the Ultimate Breakout System, a sophisticated and '
                'proprietary Expert Advisor (EA) met',
 'images': ['https://c.mql5.com/31/1420/ultimate-breakout-system-logo-200x200-3928.png'],
 'market_id': 'mt5',
 'name': 'Ultimate Breakout System',
 'price': None,
 'product_id': '133653',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.726307',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Profalgo Limited'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Burning Grid EA MT5 – Multi-Pair Grid Power with Adaptive '
                'Risk \n'
                '\n'
                'Trade up to 35 forex pairs simultaneously with intelligent '
                'strategy selection, flexible risk profiles, and dynamic '
                'drawdown control.\n'
                'Actual Price: $999.00 - increases by with next 15 purchases '
                '(Next Price: $1199, Final price: $1999) Contact me to receive '
                'a time-limited, fully functional trial version! \n'
                'Manual: '
                'https://magma-software.solutions/burning-grid/bgmanual-en.html '
                'Community : https://www.mql5.com/en/messages/0151274c579fdb0',
 'images': ['https://c.mql5.com/31/1416/burning-grid-logo-200x200-1710.png'],
 'market_id': 'mt5',
 'name': 'Burning Grid',
 'price': None,
 'product_id': '135273',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.728600',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Magma Software Solutions UG'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AI Neuro Dynamics EA Adaptive Signal Architecture for XAU/USD '
                '| H1 AI Neuro Dynamics is more than just an Expert Advisor — '
                'it is a modular cognitive trading system built for precision '
                'and adaptability on the XAU/USD (Gold) pair. Designed for '
                'high-volatility environments, it fully complies with the '
                'performance and risk requirements of prop firm standards. '
                'Powered by a proprietary neuro-quantum decision architecture '
                ', the EA evaluates market structure in real time, dynamically '
                'adjusting its inter',
 'images': ['https://c.mql5.com/31/1376/ai-neuro-dynamics-mt5-logo-200x200-8040.png'],
 'market_id': 'mt5',
 'name': 'AI Neuro Dynamics MT5',
 'price': None,
 'product_id': '134465',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.730702',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Peter Robert Grange'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Monitoring of real trading Advisor -\xa0 '
                'https://www.mql5.com/en/signals/2264971 My other '
                'products\xa0 \xa0 -\xa0 \xa0 \xa0 click here Keep in mind '
                'that the results on different brokers may differ, I recommend '
                'testing on your broker before using it\xa0(you can ask me for '
                'a list of recommended brokers in the PM). Read the blog post '
                'with the description of the adviser before starting work and '
                'if you have any additional questions, write to me in the PM. '
                'A fully automatic Expert Advisor that does not require '
                'additional',
 'images': ['https://c.mql5.com/31/1206/king-sniper-ea-logo-200x200-7179.png'],
 'market_id': 'mt5',
 'name': 'King Sniper EA',
 'price': None,
 'product_id': '124990',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.732738',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ivan Bebikov'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'We proudly present our cutting-edge robot, the\xa0 Big Forex '
                'Players EA \xa0designed to maximize your trading potential, '
                'minimize emotional trading, and make smarter decisions '
                'powered by cutting-edge technology. The whole system in this '
                'EA took us many months to build, and then we spent a lot of '
                'time testing it.\xa0This unique EA includes three distinct '
                'strategies that can be used independently or in together.\xa0'
                'The robot receives the positions of the\xa0 biggest\xa0'
                'Banks \xa0(positions are sent from our database t',
 'images': ['https://c.mql5.com/31/1042/big-forex-players-mt5-logo-200x200-3102.png'],
 'market_id': 'mt5',
 'name': 'Big Forex Players MT5',
 'price': None,
 'product_id': '92199',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.737389',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing our advanced Scalping Forex Robot. The scalping '
                'algorithm is built to spot high-probability entry and exit '
                'points, ensuring that every trade is executed with the '
                'highest chance of success within the M1 timeframe . The best '
                'pair to use with the Scalping Robot is XAUUSD .This robot is '
                'perfect for traders who prefer the scalping method and want '
                'to take advantage of rapid price movements without having to '
                'manually monitor the charts. It is suitable for both '
                'beginners looking for an autom',
 'images': ['https://c.mql5.com/31/1243/scalping-robot-mt5-logo-200x200-4701.png'],
 'market_id': 'mt5',
 'name': 'Scalping Robot MT5',
 'price': None,
 'product_id': '127704',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.739565',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': "9 copies left at $599 Next price $699 Hey traders, If you're "
                "looking for an EA that doesn't just fire off trades for the "
                'sake of activity, but actually follows a smart, battle-tested '
                'strategy — meet Scalper Investor EA. This is a multi-currency '
                'expert advisor already armed with a solid reversal strategy, '
                'and soon to be upgraded with a trend-following module. Ready '
                'to trade: The Reversal Strategy\n'
                'At launch, Scalper Investor EA comes fully loaded with a '
                'reversal system designed to catch pullbacks',
 'images': ['https://c.mql5.com/31/1395/scalper-investor-logo-200x200-6238.png'],
 'market_id': 'mt5',
 'name': 'Scalper Investor',
 'price': None,
 'product_id': '139303',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.741685',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'Each buyer of this indicator also receives the following for '
                'free:\n'
                'The custom utility "Bomber Utility", which automatically '
                'manages every trade, sets Stop Loss and Take Profit levels, '
                'and closes trades according to the rules of this strategy Set '
                'files for configuring the indicator for various assets Set '
                'files for configuring Bomber Utility in the following modes: '
                '"Minimum Risk", "Balanced Risk", and "Wait-and-See Strategy" '
                'A step-by-step video manual to help you quickly install, '
                'configure, and s',
 'images': ['https://c.mql5.com/31/1354/divergence-bomber-logo-200x200-2956.png'],
 'market_id': 'mt5',
 'name': 'Divergence Bomber',
 'price': None,
 'product_id': '136033',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.744129',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Bonnitta EA \xa0is based on Pending Position strategy ( PPS ) '
                'and a very advanced secretive trading algorithm. The strategy '
                'of\xa0 Bonnitta EA \xa0is a combination of a secretive custom '
                'indicator, Trendlines, Support & Resistance levels ( Price '
                'Action ) and most important secretive trading algorithm '
                "mentioned above. DON'T BUY AN EA WITHOUT ANY REAL MONEY TEST "
                'OF MORE THAN 3 MONTHS, IT TOOK ME MORE THAN 100 WEEKS(MORE '
                'THAN 2 YEARS) TO TEST BONNITTA EA ON REAL MONEY AND SEE THE '
                'RESULT ON THE LINK BELOW. B',
 'images': ['https://c.mql5.com/31/825/bonnitta-ea-mt5-logo-200x200-9279.png'],
 'market_id': 'mt5',
 'name': 'Bonnitta EA MT5',
 'price': None,
 'product_id': '52212',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.746206',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ugochukwu Mobi'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'GoldenHour Expert Advisor\xa0 A precision scalping system for '
                'XAUUSD (Gold) that focuses on high-probability '
                'single-position trades. The EA executes 2-3 carefully '
                'selected trades per day during optimal market conditions, '
                'avoiding risky multi-position or martingale strategies. \n'
                'NEXT price 599$\xa0\xa0 " If the EA doesn\'t perform well in '
                'your backtest, please feel free to message me. I’ll be happy '
                'to help you set it up correctly and get the best possible '
                'results ." \n'
                'Trading Approach: - Single position tra',
 'images': ['https://c.mql5.com/31/1383/goldenhour-logo-200x200-9546.png'],
 'market_id': 'mt5',
 'name': 'GoldenHour',
 'price': None,
 'product_id': '132932',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.750667',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Zaha Feiz'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The GBPUSD Robot MT5 is an advanced automated trading system '
                'meticulously designed for the specific dynamics of the \xa0 '
                'GBP/USD \xa0 currency pair. Utilizing advanced technical '
                'analysis, the robot assesses historical and real-time data '
                'to \xa0 identify potential trends , key support and '
                'resistance levels, and other relevant market signals specific '
                'to GBP/USD.\xa0 The Robot opens positions\xa0 every day,\xa0 '
                'from Monday to Friday, and\xa0 all positions are secured \xa0'
                'with Take Profit, Stop Loss, Trailing Stop, Break-E',
 'images': ['https://c.mql5.com/31/1200/gbpusd-robot-mt5-logo-200x200-8029.png'],
 'market_id': 'mt5',
 'name': 'GbpUsd Robot MT5',
 'price': None,
 'product_id': '123850',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.753122',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Forex EA Trading Channel on MQL5:\xa0 Join my MQL5 channel to '
                'update the latest news from me.\xa0 My community of over '
                '14,000 members on MQL5 . \n'
                'ONLY 3 COPIES OUT OF 10 LEFT AT $399! After that, the price '
                'will be raised to $499.\n'
                '\n'
                '- REAL SIGNAL\xa0 Low Risk:\xa0 '
                'https://www.mql5.com/en/signals/2302784 \n'
                'IC Markets - High Risk: \xa0 '
                'https://www.mql5.com/en/signals/2310008 \n'
                'Full installation instructions for EA AI Gold Sniper to work '
                'properly are updated at \xa0 comment #3 \n'
                '\n'
                'AI Gold Sniper applies the latest GPT-4o',
 'images': ['https://c.mql5.com/31/1358/ai-gold-scalper-mt5-logo-200x200-8759.png'],
 'market_id': 'mt5',
 'name': 'AI Gold Sniper MT5',
 'price': None,
 'product_id': '133197',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.755256',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ho Tuan Thang'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Neuron is a distinctive Expert Advisor that continues '
                'the Aura series of trading systems. By leveraging advanced '
                'Neural Networks and cutting-edge classic trading strategies, '
                'Aura Neuron offers an innovative approach with excellent '
                'potential performance. Fully automated, this Expert Advisor '
                'is designed to trade currency pairs such as XAUUSD (GOLD). It '
                'has demonstrated consistent stability across these pairs from '
                '1999 to 2023. The system avoids dangerous money management '
                'techniques, such as m',
 'images': ['https://c.mql5.com/31/1359/aura-neuron-mt5-logo-200x200-4436.png'],
 'market_id': 'mt5',
 'name': 'Aura Neuron MT5',
 'price': None,
 'product_id': '123089',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.757292',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'By popular demand from my large Xpert community (15,000+ '
                'downloads) , I’ve developed a fully automated Expert Advisor '
                'for the highly volatile gold market (XAUUSD) – a powerful '
                'solution for traders looking to capitalize on breakouts, '
                'follow trends, and utilize multiple signals per week . '
                'GoldXpert is designed specifically for beginners and '
                'semi-professionals , offering precise analysis to optimize '
                'your trading strategy and targeting those who want to benefit '
                'from dynamic market movements . Built',
 'images': ['https://c.mql5.com/31/1392/goldxpert-mt5-logo-200x200-1249.png'],
 'market_id': 'mt5',
 'name': 'GoldXpert MT5',
 'price': None,
 'product_id': '138998',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.759422',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Steve Rosenstock'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Way To Stars is an automated trading system based on the '
                'classic night scalping logic, designed to capture short-term '
                'opportunities during the lowest volatility periods of the '
                'market. \n'
                'Nighttime trading tends to have lower noise and weaker '
                'trends, making it suitable for high-frequency and precise '
                'operations. This type of strategy has existed in the field of '
                'algorithmic trading for over two decades. Way To Stars '
                'inherits this mature framework and rebuilds its algorithm to '
                'fully adapt to current',
 'images': ['https://c.mql5.com/31/1093/way-to-stars-mt5-logo-200x200-9461.png'],
 'market_id': 'mt5',
 'name': 'Way To Stars MT5',
 'price': None,
 'product_id': '113589',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.764245',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Wei Tu'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Gold Phoenix GPT - The Ultimate AI Trading Tool for Gold '
                'Pairs Introducing Gold Phoenix GPT, the most thorough and '
                'honest implementation of AI for gold trading. Built on '
                'real-world performance, this EA is specifically designed for '
                'gold pairs, using a powerful breakout strategy on the M1 '
                'timeframe. Unlike many tools that perform well in backtests '
                'but fall short in live conditions, Gold Phoenix GPT excels '
                'where it matters most: in fast-moving live gold markets. '
                'Powered by advanced AI—including Cha',
 'images': ['https://c.mql5.com/31/1219/the-gold-phoenix-g-p-t-logo-200x200-9392.png'],
 'market_id': 'mt5',
 'name': 'The Gold Phoenix',
 'price': None,
 'product_id': '124335',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.766478',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Black Edition\xa0is a fully automated EA designed to '
                'trade\xa0GOLD\xa0only. Expert showed stable results on XAUUSD '
                'in 2011-2020 period. No dangerous methods of money management '
                'used, no martingale, no grid or scalp. Suitable for any '
                'broker conditions. EA trained with a\xa0multilayer '
                'perceptron\xa0Neural Network\xa0(MLP) is a class of\xa0'
                'feedforward\xa0artificial neural network\xa0(ANN). The term '
                'MLP is used ambiguously, sometimes loosely to\xa0any\xa0'
                'feedforward ANN, sometimes strictly to refer to networks '
                'composed of mult',
 'images': ['https://c.mql5.com/31/1386/aura-black-edition-mt5-logo-200x200-8408.png'],
 'market_id': 'mt5',
 'name': 'Aura Black Edition MT5',
 'price': None,
 'product_id': '64961',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.768597',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '【突破性US30交易策略】 '
                '这款EA专为US30指数（道琼斯工业平均指数）量身定制，融合先进的时间管理与动态风控技术，为您打造前瞻性且精准高效的交易体验。 '
                '为不同经验水平的交易者提供简便高效的交易体验，并支持个性化设置，满足多样化需求 ，在这里都能找到符合自己需求的操作模式。 '
                '如需查看该策略的交易表现: Live account signal \n'
                '\n'
                '专属资产优化 ：专为US30设计，精准捕捉市场脉动，不适用于其他货币对或资产。 灵活时间框架 '
                '：适用于任意时间框架，随时随地灵活操作。 个性化止损与止盈 '
                '：根据您的风控需求，自定义设置止损、止盈及追踪止损功能，确保风险与收益达到最佳平衡。 动态仓位管理 '
                '：支持固定手数与动态手数分配，满足不同账户规模的需求，提升资金效率。 小资金友好 '
                '：即使是较小资金账户也可顺利运行，体验专业级交易。 平台兼容推荐 ：建议使用对冲账户模式与高流动性平台（例如IC '
                'Market），并推荐在H1时间框架运行 。',
 'images': ['https://c.mql5.com/31/1318/apex-flow-logo-200x200-9015.png'],
 'market_id': 'mt5',
 'name': 'Apex Flow',
 'price': None,
 'product_id': '133492',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.771077',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dong Zhi Sun'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Venom Gold Pro: Automated XAUUSD M30 EA Venom Gold Pro is a '
                "fully automated Expert Advisor. It's built for XAUUSD (Gold) "
                'on the M30 timeframe . This system uses advanced, pure price '
                'action logic. It monitors market behavior and executes trades '
                'in real-time. No indicators or martingale methods are used. '
                'We focus on clean, rule-based execution. Venom Gold Pro '
                'offers four proven, high-win-rate strategies . You can select '
                'the style that best fits your trading approach. Key Features '
                'Automated Tradin',
 'images': ['https://c.mql5.com/31/1418/venom-gold-pro-logo-200x200-1559.png'],
 'market_id': 'mt5',
 'name': 'Venom Gold Pro',
 'price': None,
 'product_id': '135571',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.773110',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antoine Melhem'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Generate consistent returns with a Grok3 AI-assisted , '
                'risk-diversified and Bitcoin-boosted EA . RiskKILLER AI is a '
                'breakout scalping algorithm identifying key levels for '
                'potential high-volatility moves, selecting best risk-reward '
                'trades while diversifying risk on 5 assets. $399: 4 copies '
                'left\xa0 / 10 . After purchase, to get the API key and the '
                'User Manual, 1. post a comment asking for them 2. mail me '
                'directly (mail findable in the dedicated group - see below). '
                '[ Live Signal ] - [\xa0 Specs & Set-up',
 'images': ['https://c.mql5.com/31/1420/risk-killer-ai-mt5-logo-200x200-7662.png'],
 'market_id': 'mt5',
 'name': 'Risk Killer AI MT5',
 'price': None,
 'product_id': '139184',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.778005',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Christophe Pa Trouillas'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AlphaCore X The AlphaCore X EA is a cutting-edge trading '
                'system that masters the complexity of financial markets with '
                'a unique combination of AI-driven analyses and data-based '
                'algorithms. By integrating ChatGPT-o1 , the latest GPT-4.5 , '
                'advanced machine learning models, and a robust big data '
                'approach, AlphaCore X achieves a new level of precision, '
                'adaptability, and efficiency. This Expert Advisor impresses '
                'with its innovative strategy, seamless AI interaction, and '
                'comprehensive additional featu',
 'images': ['https://c.mql5.com/31/1396/alphacore-x-logo-200x200-2807.png'],
 'market_id': 'mt5',
 'name': 'AlphaCore X',
 'price': None,
 'product_id': '139373',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.780251',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Arseny Potyekhin'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Bitcoin Robot Grid MT5 is an intelligent trading system '
                'designed to automate BTCUSD trading using the grid trading '
                'strategy. This method takes advantage of market fluctuations '
                'by placing a structured series of buy and sell orders at '
                'predefined price levels. The robot continuously monitors '
                'market conditions and executes trades according to its preset '
                'parameters, allowing for consistent market engagement without '
                'the need for manual intervention. Bitcoin Robot Grid is the '
                'perfect solution for trad',
 'images': ['https://c.mql5.com/31/1256/bitcoin-robot-grid-mt5-logo-200x200-5077.png'],
 'market_id': 'mt5',
 'name': 'Bitcoin Robot Grid MT5',
 'price': None,
 'product_id': '128620',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.804421',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Golden Algo\xa0– The Ultimate AI-Powered Expert Advisor for '
                'Gold Traders Golden Algo Expert Advisor is a powerful trading '
                'system designed specifically for XAUUSD (Gold). It combines '
                'technical indicators with real-time market data—including the '
                'US Index and market sentiment—to generate precise trade '
                'signals. Each signal is then filtered through an advanced '
                'OpenAI-powered process to ensure only high-probability trades '
                'are executed. By blending technical analysis, fundamental '
                'insights, and artificial',
 'images': ['https://c.mql5.com/31/1329/golden-algo-logo-200x200-4853.png'],
 'market_id': 'mt5',
 'name': 'Golden Algo',
 'price': None,
 'product_id': '131124',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.807423',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ramethara Vijayanathan'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'ScalpPrime \xa0 EA \xa0 \n'
                'Advanced Dual-Strategy Expert Advisor for Structured and '
                'Rule-Based Gold Scalping \xa0 ScalpPrime \xa0 EA is a '
                'precision-engineered Expert Advisor tailored for trading '
                'XAUUSD (Gold) using short-term. It is built on a '
                'professional-grade, rule-based framework that combines two '
                'complementary approaches: a Fibonacci retracement strategy '
                'for \xa0 identifying \xa0 high-probability price zones and a '
                'volume-based confirmation system for filtering market noise '
                'and validating trade entries. \xa0 Desi',
 'images': ['https://c.mql5.com/31/1395/scalpprime-logo-200x200-6661.png'],
 'market_id': 'mt5',
 'name': 'ScalpPrime',
 'price': None,
 'product_id': '138544',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.809802',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'DRT Circle'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Bitcoin Hash EA is a distinctive Expert Advisor that '
                'continues the Aura series of trading systems. By leveraging '
                'advanced Neural Networks and cutting-edge classic trading '
                'strategies, Aura BTC offers an innovative approach with '
                'excellent potential performance. Fully automated, this Expert '
                'Advisor is designed to trade currency pair BTCUSD (Bitcoin). '
                'It has demonstrated consistent stability across these pairs '
                'from 2017 to 2025. The system avoids dangerous money '
                'management techniques, such as m',
 'images': ['https://c.mql5.com/31/1347/aura-bitcoin-hash-logo-200x200-8237.png'],
 'market_id': 'mt5',
 'name': 'Aura Bitcoin Hash',
 'price': None,
 'product_id': '135582',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.812418',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'EA Gold Stuff mt5\xa0is an Expert Advisor designed '
                'specifically for trading gold. The operation is based on '
                'opening orders using the \xa0Gold Stuff mt5 \xa0indicator, '
                'thus the EA works according to the "Trend Follow" strategy, '
                'which means following the trend. For Expert Advisor need '
                'hedge type account\xa0 Contact me immediately after the '
                'purchase to get personal bonus!\xa0 You can get a free copy '
                'of our Strong Support and Trend Scanner indicator, please pm. '
                'me! Settings\xa0 and manual \xa0 here\xa0 \n'
                'Please note that I',
 'images': ['https://c.mql5.com/31/1340/ea-gold-stuff-mt5-logo-200x200-7851.png'],
 'market_id': 'mt5',
 'name': 'EA Gold Stuff mt5',
 'price': None,
 'product_id': '54468',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.816287',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vasiliy Strukov'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Automated Zone-Adaptive Impulse Logic and AI in an EA\n'
                'INFusion is a fully automated Expert Advisor that trades '
                'exclusively the gold market (XAU/USD). Designed for '
                'MetaTrader, the system uses current GPT models\xa0 GPT-o4 '
                'high to analyze price data in real time. The EA combines '
                'modern AI methods with a proprietary price logic, creating a '
                'trading approach that flexibly adapts to market behavior – '
                'without traditional indicators, without martingale, grid or '
                'news trading. INFusion \xa0 is aimed at users s',
 'images': ['https://c.mql5.com/31/1380/quantedgex-logo-200x200-8739.png'],
 'market_id': 'mt5',
 'name': 'INFusion',
 'price': None,
 'product_id': '138379',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.819199',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Viktoriya Volgina'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Hello everyone, let me introduce myself:\n'
                '\n'
                'I am Quantum StarMan, the electrifying, freshest member of '
                'the Quantum EAs family. \n'
                '\n'
                "I'm a fully automated, multicurrency EA with the power to "
                'handle up to 5 dynamic pairs: AUDUSD, EURAUD, EURUSD, GBPUSD, '
                'and USDCAD . With the utmost precision and unwavering '
                "responsibility, I'll take your trading game to the next "
                "level. Here's the kicker: I don't rely on Martingale "
                'strategies. Instead, I utilize a sophisticated grid system '
                "that's designed for peak perfor",
 'images': ['https://c.mql5.com/31/1404/quantum-starman-logo-200x200-9143.png'],
 'market_id': 'mt5',
 'name': 'Quantum StarMan',
 'price': None,
 'product_id': '107189',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.821513',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AI ZeroPoint Dynamics EA Cognitive Signal Architecture | '
                'Multi-Asset Precision Engine “Not an EA. Not a strategy. A '
                'living system of inference, adaptation, and execution.” BORN '
                'FROM THE ZERO POINT AI ZeroPoint Dynamics EA is not built — '
                'it is calibrated.\n'
                'Not coded — but architected to function as a real-time '
                'cognitive organism , responding to markets with a depth of '
                'reasoning that mirrors human decision-making — yet surpasses '
                'it in scale, consistency, and velocity. At the heart of '
                'ZeroPoint lie',
 'images': ['https://c.mql5.com/31/1383/ai-zeropoint-dynamics-mt5-logo-200x200-4072.png'],
 'market_id': 'mt5',
 'name': 'AI ZeroPoint Dynamics MT5',
 'price': None,
 'product_id': '138199',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.823508',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Peter Robert Grange'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The XG Gold Robot MT5 is specially designed for Gold. We '
                'decided to include this EA in our offering after extensive '
                'testing .\xa0XG Gold Robot and works perfectly with the '
                'XAUUSD, GOLD, XAUEUR pairs. XG Gold Robot has been created '
                'for all traders who like to Trade in Gold and includes '
                'additional a function that displays weekly Gold levels with '
                'the minimum and maximum displayed in the panel as well as on '
                'the chart, which will help you in manual trading. It’s a '
                'strategy based on Price Action, Cycle S',
 'images': ['https://c.mql5.com/31/1058/xg-gold-robot-mt5-logo-200x200-7278.png'],
 'market_id': 'mt5',
 'name': 'XG Gold Robot MT5',
 'price': None,
 'product_id': '92195',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.825933',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Welcome to Trade Manager MT5 - the ultimate risk management '
                'tool designed to make trading more intuitive, precise, and '
                "efficient. This is not just an order placement tool; it's a "
                'comprehensive solution for seamless trade planning, position '
                "management, and enhanced control over risk. Whether you're a "
                'beginner taking your first steps, an advanced trader, or a '
                'scalper needing rapid executions, Trade Manager MT5 adapts to '
                'your needs, offering flexibility across all markets, from '
                'forex and indices t',
 'images': ['https://c.mql5.com/31/405/forex-trade-manager-mt5-logo-200x200-4875.png'],
 'market_id': 'mt5',
 'name': 'Forex Trade Manager MT5',
 'price': None,
 'product_id': '39150',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.830329',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'InvestSoft'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Eternal Engine is an advanced EA that integrates multiple '
                'indicators with grid and Martingale strategies. Its core '
                'feature is precise entry point control, enabling it to '
                'perform exceptionally well even in complex market '
                'environments. Eternal Engine EA offers numerous trading '
                'opportunities, is not sensitive to spreads, and ensures '
                'accurate execution of every trade through strict entry point '
                'management. The strategy has been proven in live trading, '
                'providing over a year of low-drawdown real-time s',
 'images': ['https://c.mql5.com/31/1008/eternal-engine-ea-mt5-logo-200x200-9599.png'],
 'market_id': 'mt5',
 'name': 'Eternal Engine EA MT5',
 'price': None,
 'product_id': '109036',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.832763',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Wei Tu'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'PUMPING STATION – Your Personal All-inclusive strategy\n'
                'Introducing\xa0PUMPING STATION\xa0— a revolutionary Forex '
                'indicator that will transform your trading into an exciting '
                'and effective activity! This indicator is not just an '
                'assistant but a full-fledged trading system with powerful '
                'algorithms that will help you start trading more stable! When '
                'you purchase this product, you also get FOR FREE: Exclusive '
                'Set Files:\xa0For automatic setup and maximum performance. '
                'Step-by-step video manual:\xa0Learn how to tr',
 'images': ['https://c.mql5.com/31/1323/algo-pumping-logo-200x200-1904.png'],
 'market_id': 'mt5',
 'name': 'Algo Pumping',
 'price': None,
 'product_id': '134062',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.834821',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'BreakTrue AI: Major levels breakout / rebound trading system '
                'with AI assistance. AI is optional and is used in the way it '
                'is supposed to be. \n'
                'Finally, the Expert Advisor which uses AI in the right way! '
                'BreakTrue AI combines sophisticated built-in trading strategy '
                'based on major levels true and false breakouts, with the '
                'cutting-edge technology of OpenAI’s ChatGPT which servers as '
                'additional entry filter. This isn’t just another empty claim '
                '— BreakTrue AI provides a genuine, fully integrated AI so',
 'images': ['https://c.mql5.com/31/1408/breaktrue-ai-mt5-logo-200x200-7785.png'],
 'market_id': 'mt5',
 'name': 'BreakTrue AI MT5',
 'price': None,
 'product_id': '140377',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.837176',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Andrey Barinov'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The Bitcoin Robot MT5 is engineered to execute Bitcoin trades '
                'with unparalleled efficiency and precision . Developed by a '
                'team of experienced traders and developers, our Bitcoin Robot '
                'employs a sophisticated algorithmic approach (price action, '
                'trend as well as two personalized indicators) to analyze '
                'market and execute trades swiftly with M5 timeframe , '
                'ensuring that you never miss out on lucrative opportunities. '
                'No grid, no martingale, no hedging, EA only open one position '
                'at the same time. Bit',
 'images': ['https://c.mql5.com/31/1079/bitcoin-robot-mt5-logo-200x200-2796.png'],
 'market_id': 'mt5',
 'name': 'Bitcoin Robot MT5',
 'price': None,
 'product_id': '114522',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.859205',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Trade Panel is a multifunctional trading assistant. The '
                'application contains more than 50 trading functions for '
                'manual trading and allows you to automate most trading '
                'operations. Instructions for installing the application | '
                'Instructions for the application | Trial version of the '
                'application for a demo account Trade. Allows you to perform '
                'trading operations in one click: Open pending orders and '
                'positions with automatic risk calculation. Open multiple '
                'orders and positions with one click. Open ord',
 'images': ['https://c.mql5.com/31/1369/tradepanel-mt5-logo-200x200-2148.png'],
 'market_id': 'mt5',
 'name': 'TradePanel MT5',
 'price': None,
 'product_id': '35049',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.861756',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Alfiya Fazylova'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Venom US30 Scalp — Pure Precision for US30 Trading Venom US30 '
                'Scalp is a fully automated Expert Advisor built for US30 (Dow '
                'Jones Index) on the M30 timeframe . It runs on a proprietary '
                'mathematical engine — no indicators, no martingale, no grid — '
                'just clean, logic-based trading. SIGNAL : Ask for signal Core '
                'Features Trend-following strategy with multi-layer '
                'confirmations Default risk: 0.01 lot per $500 (adjustable) '
                'Internal controls to reduce risk exposure Simple '
                'plug-and-play setup, no optimiz',
 'images': ['https://c.mql5.com/31/1366/venom-us30-scalp-logo-200x200-5259.png'],
 'market_id': 'mt5',
 'name': 'Venom Us30 Scalp',
 'price': None,
 'product_id': '136145',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.863634',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antoine Melhem'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing the DS Gold Robot, your ultimate companion in '
                'navigating the intricate world of XAUUSD trading. Developed '
                'with precision and powered by cutting-edge algorithms, DS '
                'Gold is a forex robot meticulously crafted to optimize your '
                'trading performance with\xa0 XAUUSD pairs . With its advanced '
                'analytical capabilities,\xa0 DS Gold \xa0Robot \xa0 '
                'constantly monitors the gold market, identifying key trends , '
                'patterns, and price movements with lightning speed. The DS '
                'Gold Robot opens positions every day from',
 'images': ['https://c.mql5.com/31/1182/ds-gold-robot-mt5-logo-200x200-9360.png'],
 'market_id': 'mt5',
 'name': 'DS Gold Robot MT5',
 'price': None,
 'product_id': '122975',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.865414',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'EvoTrade: The First Self-Learning Trading System on the '
                'Market Allow me to introduce EvoTrade —a unique trading '
                'advisor built using cutting-edge technologies in computer '
                'vision and data analysis. It is the first self-learning '
                'trading system on the market, operating in real-time. '
                'EvoTrade analyzes market conditions, adapts strategies, and '
                'dynamically adjusts to changes, delivering exceptional '
                'precision in any environment. EvoTrade employs advanced '
                'neural networks, including Long Short-Term Memory',
 'images': ['https://c.mql5.com/31/1267/evotrade-ea-mt5-logo-200x200-5183.png'],
 'market_id': 'mt5',
 'name': 'EvoTrade EA MT5',
 'price': None,
 'product_id': '129661',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.867513',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dolores Martin Munoz'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Fully Automated EA based on Supply and Demand Principles. The '
                'first Supply and Demand EA that is offering Complete '
                'Automation . Now trading Becomes Effortless, offering full '
                'control over your trading strategy through a User-Friendly '
                'graphical Trading Panel. You get a Super High Quality '
                'Algorithmic Trading Software that covers all trading styles '
                'Manual, Semi-Auto and Full-Auto.\xa0Through various settings '
                'and customization options, every trader can create a strategy '
                'that fits their own needs and per',
 'images': ['https://c.mql5.com/31/1110/supply-demand-ea-probot-mt5-logo-200x200-5181.png'],
 'market_id': 'mt5',
 'name': 'Supply Demand EA ProBot MT5',
 'price': None,
 'product_id': '117023',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.871526',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Georgios Kalomoiropoulos'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'PROP FIRM READY! ( download SETFILE ) LAUNCH PROMO: Only a '
                'few copies left at current price! Final price: 990$ Get 1 EA '
                'for free (for 2 trade accounts) -> contact me after purchase '
                'Ultimate Combo Deal \xa0 -> \xa0 click here JOIN PUBLIC '
                'GROUP: \xa0 Click here \n'
                'Live Signal\n'
                '\n'
                'Welcome to the Gold Reaper! Build on the very succesfull '
                'Goldtrade Pro, this EA has been designed to run on multiple '
                'timeframes at the same time, and has the option to set the '
                'trade frequency from very conservative to extreme volatile',
 'images': ['https://c.mql5.com/31/1365/the-gold-reaper-mt5-logo-200x200-7714.png'],
 'market_id': 'mt5',
 'name': 'The Gold Reaper MT5',
 'price': None,
 'product_id': '111357',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.874714',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Profalgo Limited'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'SmartChoise EA – Neural Network–Powered Trading System for '
                'XAU/USD (Gold) on M1 Timeframe\xa0\n'
                '\n'
                'The user manual is available via the link in my profile '
                'page.\n'
                'This EA is built for long-term, controlled '
                'growth—understanding and aligning it with your risk tolerance '
                'is key to its success. Uses a neural network–based engine '
                'that continuously analyzes real-time market data to adapt '
                'trading strategies according to current market conditions. '
                'This approach helps optimize trade entries, improve risk '
                'control,',
 'images': ['https://c.mql5.com/31/1305/smartchoise-logo-200x200-7053.png'],
 'market_id': 'mt5',
 'name': 'SmartChoise',
 'price': None,
 'product_id': '128606',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.877001',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Gabriel Costin Floricel'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aria Connector EA (MT5 to ChatGPT and more...) Public '
                'channel:\xa0 https://www.mql5.com/en/channels/binaryforexea \n'
                'Many EAs on the market claim to use artificial intelligence '
                'or "neural networks" when in reality they only run '
                'traditional logic or connect with unreliable sources. Aria '
                'Connector EA was created with a clear and transparent '
                'purpose: to directly connect your MT5 platform with OpenAI’s '
                'AI , with no middlemen or shady scripts. From its first '
                'version, Aria establishes a real connection wit',
 'images': ['https://c.mql5.com/31/1412/aria-connector-ea-logo-200x200-8566.png'],
 'market_id': 'mt5',
 'name': 'ARIA Connector EA',
 'price': None,
 'product_id': '140434',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.878988',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Martin Alejandro Bamonte'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Big sale 50% OFF! Price $750. Regular price $1499 All our '
                'signals are now available on myfxbook: \xa0 click here \xa0 '
                'Unique set files and all recommendations are provided free of '
                'charge. All future updates of the adviser are included in the '
                'price. After the purchase, contact me and I will help you '
                'install and configure the robot correctly. I will also share '
                'with you information on how to get a free VPS from a reliable '
                'broker. Gold is one of the riskiest instruments on the '
                'market. It requires precisi',
 'images': ['https://c.mql5.com/31/1394/prometheus-mt5-logo-200x200-1628.png'],
 'market_id': 'mt5',
 'name': 'Prometheus MT5',
 'price': None,
 'product_id': '128417',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.881460',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Evgenii Aksenov'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'DeepAlgo GPX ML - Real-time Analysis by Intelligent AI '
                'Models \n'
                'When you buy an AI EA, you have no idea if the EA is actually '
                'using AI. With DeepAlgo GPX ML, there are actual API calls '
                'directly to DeepSeek for every single trade and you can see '
                'this reflected on your API dashboard in real time. The API '
                'keys are 100% owned by you and you can manage and use them '
                'however you like. There are multiple fail-safes in place in '
                'order to manage unexpected problems with the API in case the '
                'API server is eve',
 'images': ['https://c.mql5.com/31/1378/deepalgo-gpx-ml-logo-200x200-2368.png'],
 'market_id': 'mt5',
 'name': 'DeepAlgo GPX ML',
 'price': None,
 'product_id': '136885',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.946965',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Connor Michael Woodson'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing Scalper Deriv: Elevating Your Scalping '
                'Experience. Important : Scalper Deriv is ONLY sold through '
                'mql5.com. Any other website is a scam and is not Scalper '
                'Deriv.\xa0 To download the updated configuration files and '
                'additional strategies, click here For the user guide, click '
                'here . Are you one of those traders who find their passion in '
                'scalping and want to make the most of your capital? Whether '
                'you have a balance of $20, $200, $2000, $20000, or even '
                '$200000 in your account, we have the p',
 'images': ['https://c.mql5.com/31/1230/scalper-deriv-logo-200x200-9680.png'],
 'market_id': 'mt5',
 'name': 'Scalper Deriv',
 'price': None,
 'product_id': '104565',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.949342',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antonio Simon Del Vecchio'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing Version 8.2—A Revolutionary Leap in AI Trading '
                "Technology I'm proud to announce my most significant update "
                'yet: Version 8.2. This groundbreaking release introduces AI '
                'Position Management and AI Web Search, which dynamically '
                'modifies Take Profit and Stop Loss levels in real-time while '
                'searching the internet for breaking news, political events, '
                'interest rates, and market sentiment, ensuring optimal '
                'position management with priority handling across all '
                'symbols. Version 8.2 harnesses th',
 'images': ['https://c.mql5.com/31/1401/mean-machine-logo-200x200-6801.png'],
 'market_id': 'mt5',
 'name': 'Mean Machine',
 'price': None,
 'product_id': '122619',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.951475',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Fusion of Tradition and Innovation—Ushering in a New Era of '
                'Intelligent Trading 【Product Essence】 This EA is specifically '
                'designed for international gold (XAUUSD), ingeniously '
                'combining classic quantitative trading models with modern '
                'intelligent analytical technologies. It deeply explores '
                'market structure and volatility characteristics to achieve '
                'robust and efficient capital growth. The strategy '
                'demonstrates a steadily rising equity curve and low drawdown '
                'in various market environments, making t',
 'images': ['https://c.mql5.com/31/1406/goldmasterfusion-mt5-logo-200x200-6749.png'],
 'market_id': 'mt5',
 'name': 'GoldMasterFusion MT5',
 'price': None,
 'product_id': '140128',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.953707',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Chen Jia Qi'}
2025-06-13 14:52:29 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Experience exceptionally fast trade copying with the \xa0 '
                'Local Trade Copier EA MT5 . With its easy 1-minute setup, '
                'this trade copier allows you to copy trades between multiple '
                'MetaTrader terminals on the same Windows computer or Windows '
                'VPS with lightning-fast copying speeds of under 0.5 seconds. '
                "Whether you're a beginner or a professional trader, the \xa0 "
                'Local Trade Copier EA MT5 \xa0 offers a wide range of options '
                "to customize it to your specific needs. It's the ultimate "
                'solution for anyone looking t',
 'images': ['https://c.mql5.com/31/946/local-trade-copier-ea-mt5-logo-200x200-6081.png'],
 'market_id': 'mt5',
 'name': 'Local Trade Copier EA MT5',
 'price': None,
 'product_id': '68951',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.955777',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Juvenille Emperor Limited'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing TrendX Gold Scalper – The Next Evolution in Gold '
                'Trading Automation LAUNCH PROMO:\xa0 Only 2 copies left at '
                'current price. Next price is $553 The price will increase by '
                '$100 with every 10 purchases\xa0 Final price: $1933 \n'
                'LIVE SIGNAL ($100K account) :\xa0 '
                'https://www.mql5.com/en/signals/2304986?source=Site+Profile+Seller '
                'LIVE SIGNAL SMALL:\xa0 '
                'https://www.mql5.com/en/signals/2307472?source=Site+Profile+Seller '
                'LIVE COMBO TrendX + US30 Scalper + EURUSD Algo:\xa0 '
                'https://www.mql5.com/en/signals/23019',
 'images': ['https://c.mql5.com/31/1384/trendx-gold-scalper-logo-200x200-7715.png'],
 'market_id': 'mt5',
 'name': 'TrendX Gold Scalper',
 'price': None,
 'product_id': '138632',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.960784',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Lo Thi Mai Loan'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'HFT PropFirm EA MT5 is\xa0 also known as Green Man due to its '
                'distinctive logo by Dilwyn Tng, is an Expert Advisor (EA) '
                'crafted specifically for overcoming challenges or evaluations '
                'from proprietary trading firms (prop firms) that permit '
                'High-Frequency Trading (HFT) strategies.\n'
                '\n'
                'Now Greenman\xa0 HFT PropFirm EA MT5 is fully automatic! '
                'Free\xa0 1 All-In-One Breakout EA account licence with '
                'purchase of HFT PropFirm EA MT5 \n'
                'Passing HFT MT5 Challenge Performance Monitor: \n'
                'Broker: Fusion Market\n'
                'Login:\xa0\xa0172147',
 'images': ['https://c.mql5.com/31/1114/hft-propfirm-ea-mt5-logo-200x200-8392.png'],
 'market_id': 'mt5',
 'name': 'HFT PropFirm EA MT5',
 'price': None,
 'product_id': '117386',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.962896',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dilwyn Tng'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'No Stress FX — When Trading Feels a Bit Calmer When I started '
                'building No Stress FX, I wasn’t trying to chase miracles.\n'
                'The goal was to create something that fits a slower, more '
                'mindful style —\n'
                'without pressure, without noise, and hopefully with more '
                'clarity. It’s the kind of trading approach that simply makes '
                'more sense to me. Just being transparent Live Signal:\xa0 '
                'https://www.mql5.com/en/signals/2313926 \n'
                'Yes, it’s running on a real account.\n'
                'Some traders have found it interesting, and activity is',
 'images': ['https://c.mql5.com/31/1407/ai-no-stress-fx-mt5-logo-200x200-9829.png'],
 'market_id': 'mt5',
 'name': 'AI No Stress FX MT5',
 'price': None,
 'product_id': '139996',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.965129',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Mariia Aborkina'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '3 copies left at $199\n'
                'Next price $299 Unique trading advisor for EURUSD\n'
                'The advisor is a modular trading system. It is based on an '
                'architecture in which each trading decision is formed not by '
                'a monolithic algorithm, but as a result of the interaction of '
                'independent logical blocks - indicator filters, entry '
                'conditions, exits and control rules. IMPORTANT! After '
                'purchase, send me a private message to receive the '
                'installation guide and setup instructions. Live signal '
                'Strategy XAUUSD - https://www.m',
 'images': ['https://c.mql5.com/31/1335/pure-ai-logo-200x200-4104.png'],
 'market_id': 'mt5',
 'name': 'Pure AI',
 'price': None,
 'product_id': '134947',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:29.985758',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vitali Vasilenka'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Live signal \n'
                '\n'
                'Find out more here: \xa0 '
                'https://www.mql5.com/en/users/prizmal/seller \n'
                'PrizmaL Scalper - Intraday Scalping for XAUUSD \n'
                'This trading algorithm is designed for speculative trading in '
                'the spot gold market XAUUSD.\n'
                'It employs advanced market microstructure analysis '
                'techniques, reacting to price impulses and liquidity in real '
                'time. The algorithm is not subject to swaps, making it '
                'particularly effective for active intraday trading.\n'
                'Optimized risk management and dynamic adaptation to volatil',
 'images': ['https://c.mql5.com/31/1388/prizmal-scalper-logo-200x200-7045.png'],
 'market_id': 'mt5',
 'name': 'PrizmaL Scalper',
 'price': None,
 'product_id': '135294',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:30.008040',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladimir Lekhovitser'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'Unlock the Power of Trends Trading with the Trend Screener '
                'Indicator: Your Ultimate Trend Trading Solution powered by '
                'Fuzzy Logic and Multi-Currencies System! Elevate your trading '
                'game with the Trend Screener, the revolutionary trend '
                'indicator designed to transform your Metatrader into a '
                'powerful Trend Analyzer. This comprehensive tool leverages '
                'fuzzy logic and integrates over 13 premium features and three '
                'trading strategies, offering unmatched precision and '
                'versatility. LIMITED TIME OFFER : Tre',
 'images': ['https://c.mql5.com/31/1414/trend-screener-pro-mt5-logo-200x200-7413.png'],
 'market_id': 'mt5',
 'name': 'Trend Screener Pro MT5',
 'price': None,
 'product_id': '47785',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:30.010686',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'STE S.S.COMPANY'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Our team is thrilled to introduce Trading Robot, the '
                'cutting-edge Smart Trading Expert Advisor for the MetaTrader '
                'terminal. AI Sniper is an intelligent, self-optimizing '
                'trading robot designed for MT5 . It leverages a smart '
                'algorithm and advanced trading strategies to maximize your '
                'trading potential. With 15 years of experience in trading '
                'exchanges and the stock market, we have developed innovative '
                'strategy management features, additional intelligent '
                'functions, and a user-friendly graphical inte',
 'images': ['https://c.mql5.com/31/1268/exp5-ai-sniper-for-mt5-logo-200x200-7487.png'],
 'market_id': 'mt5',
 'name': 'Exp5 AI Sniper for MT5',
 'price': None,
 'product_id': '118127',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:30.014319',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladislav Andruschenko'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Spiral Ascend EA – Smart Automated Trading for XAUUSD \n'
                'The\xa0 Spiral Ascend \xa0 EA is a powerful, fully automated '
                'trading solution tailored for the XAUUSD (Gold) market , '
                'combining classical Fibonacci methodologies with modern '
                'technical analysis and advanced volume-based logic. Developed '
                'with flexibility and precision in mind, this EA is ideal for '
                'traders seeking a disciplined and logic-driven approach to '
                'algorithmic trading—including those working under the '
                'conditions of prop trading firms . Buy S',
 'images': ['https://c.mql5.com/31/1368/spiral-ascend-logo-200x200-4715.png'],
 'market_id': 'mt5',
 'name': 'Spiral Ascend',
 'price': None,
 'product_id': '136587',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:30.017068',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'DRT Circle'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'EASY Insight AIO – All-in-One Market Scanner with Full '
                'Indicator Data What if you could analyze the entire Forex '
                'market in seconds – without plotting anything on your '
                'charts? \n'
                'EASY Insight AIO integrates the full analytical output from '
                'FX Power, FX Volume, FX Dynamic, and FX Levels – into a '
                'single, structured CSV file. No extra tools needed. No chart '
                'overlays. No distractions. \n'
                '⸻ \n'
                'Why Use EASY Insight AIO? All Indicators Included: No need '
                'for additional licenses – AIO comes with full access to',
 'images': ['https://c.mql5.com/31/1384/easyinsight-aio-mt5-logo-200x200-8116.png'],
 'market_id': 'mt5',
 'name': 'EasyInsight AIO MT5',
 'price': None,
 'product_id': '138518',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:30.021403',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Alain Verleyen'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'After 6 Years of Successful Manual Trading, My Strategies Are '
                'Now Available as Expert Advisors! \n'
                'Introducing the DAX Killer EA – a trading system built for '
                'the DAX Index from years of hands-on experience, extensive '
                'testing, and a steadfast commitment to secure, strategic '
                'trading. NO GRID, NO MARTINGALE, TIGHT SL EVERY TRADE. ONE '
                'TRADE PER DAY . \xa0 NO LOT MULTIPLIER. \xa0The price of the '
                'EA will increase by $100 with every 10 purchases. ICTRADING '
                'LIVE SIGNAL \xa0 DAX Killer Public \xa0 Chat \xa0 Group \xa0 '
                'IMPOR',
 'images': ['https://c.mql5.com/31/1337/dax-killer-logo-200x200-2452.png'],
 'market_id': 'mt5',
 'name': 'Dax Killer',
 'price': None,
 'product_id': '134248',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:30.024395',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Pablo Dominguez Sanchez'}
2025-06-13 14:52:30 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'LAUNCH PROMO: Final price: 1,700$ Only 2 copies left at $399. '
                'Next price will be $499 Get 1 EA for free (for 2 trade '
                'accounts) -> contact me after purchase Instruction Blog Link '
                'to Channel \n'
                'Welcome to ZenFlow! ZenFlow is an advanced EA designed to '
                'adapt to changing market trends with precision and speed. It '
                'is optimized to trade the XAUUSD( or GOLD) symbol and should '
                'be run on only one chart. This EA uses a sophisticated '
                'trend-following strategy combined with a momentum-based '
                'indicator that ide',
 'images': ['https://c.mql5.com/31/1318/zen-flow-2-logo-200x200-9786.png'],
 'market_id': 'mt5',
 'name': 'Zen Flow 2',
 'price': None,
 'product_id': '133718',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T14:52:30.027211',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Hamza Ashraf'}
2025-06-13 14:52:30 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 14:52:30 [market] INFO: Exported 1 items to output/market_20250613_145229.json
2025-06-13 14:52:30 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 306,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.507399,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 12, 52, 30, 35582, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'item_dropped_count': 69,
 'item_dropped_reasons_count/DropItem': 69,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 11,
 'log_count/WARNING': 73,
 'memusage/max': 75186176,
 'memusage/startup': 75186176,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 12, 52, 29, 528183, tzinfo=datetime.timezone.utc)}
2025-06-13 14:52:30 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:00:22 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:00:22 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:00:22 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:00:22 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:00:22 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:00:22 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:00:22 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:00:22 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:00:22 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:00:22 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:00:22 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:00:22 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:00:22 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:00:22 [market] INFO: Spider opened: market
2025-06-13 15:00:22 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:00:22 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 15:00:22 [scrapy.core.scraper] ERROR: Spider error processing <GET https://www.mql5.com/en/market/mt5> (referer: None)
Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 619, in xpath
    result = xpathev(
             ^^^^^^^^
  File "src/lxml/etree.pyx", line 1624, in lxml.etree._Element.xpath
  File "src/lxml/xpath.pxi", line 290, in lxml.etree.XPathElementEvaluator.__call__
  File "src/lxml/xpath.pxi", line 210, in lxml.etree._XPathEvaluatorBase._handle_result
lxml.etree.XPathEvalError: Invalid expression

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/defer.py", line 343, in iter_errback
    yield next(it)
          ^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/middlewares.py", line 17, in process_spider_output
    for i in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/depth.py", line 59, in process_spider_output
    yield from super().process_spider_output(response, result, spider)
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 63, in parse
    item['price'] = self.extract_product_price_near_link(product_link)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 116, in extract_product_price_near_link
    container = product_link.xpath('./ancestor::*[contains(@class, "product-card") or descendant::*[contains(@class, "product-card")]]/1]')
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 626, in xpath
    raise ValueError(f"XPath error: {exc} in {query}")
ValueError: XPath error: Invalid expression in ./ancestor::*[contains(@class, "product-card") or descendant::*[contains(@class, "product-card")]]/1]
2025-06-13 15:00:22 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:00:22 [market] INFO: Exported 0 items to output/market_20250613_150022.json
2025-06-13 15:00:22 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 306,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.163262,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 0, 22, 756850, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'items_per_minute': None,
 'log_count/ERROR': 1,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75116544,
 'memusage/startup': 75116544,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'spider_exceptions/ValueError': 1,
 'spider_exceptions/count': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 0, 22, 593588, tzinfo=datetime.timezone.utc)}
2025-06-13 15:00:22 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:01:40 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:01:40 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:01:40 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:01:40 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:01:40 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:01:40 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:01:41 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:01:41 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:01:41 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:01:41 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:01:41 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:01:41 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:01:41 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:01:41 [market] INFO: Spider opened: market
2025-06-13 15:01:41 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:01:41 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 15:01:41 [scrapy.core.scraper] ERROR: Spider error processing <GET https://www.mql5.com/en/market/mt5> (referer: None)
Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/defer.py", line 343, in iter_errback
    yield next(it)
          ^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/middlewares.py", line 17, in process_spider_output
    for i in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/depth.py", line 59, in process_spider_output
    yield from super().process_spider_output(response, result, spider)
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 63, in parse
    item['price'] = self.extract_product_price_near_link(product_link)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 133, in extract_product_price_near_link
    price_elements = container.css(selector).getall()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in css
    return self.__class__(flatten([x.css(query) for x in self]))
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in <listcomp>
    return self.__class__(flatten([x.css(query) for x in self]))
                                   ^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 655, in css
    return self.xpath(self._css2xpath(query))
                      ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 659, in _css2xpath
    return _ctgroup[type]["_csstranslator"].css_to_xpath(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/csstranslator.py", line 137, in css_to_xpath
    return super().css_to_xpath(css, prefix)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/xpath.py", line 223, in css_to_xpath
    for selector in parse(css)
                    ^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 529, in parse
    return list(parse_selector_group(stream))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 544, in parse_selector_group
    yield Selector(*parse_selector(stream))
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 553, in parse_selector
    result, pseudo_element = parse_simple_selector(stream)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 620, in parse_simple_selector
    result = parse_attrib(result, stream)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 786, in parse_attrib
    raise SelectorSyntaxError(f"Operator expected, got {next}")
cssselect.parser.SelectorSyntaxError: Operator expected, got <DELIM '(' at 10>
2025-06-13 15:01:41 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:01:41 [market] INFO: Exported 0 items to output/market_20250613_150141.json
2025-06-13 15:01:41 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 310,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.156237,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 1, 41, 214511, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'items_per_minute': None,
 'log_count/ERROR': 1,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75149312,
 'memusage/startup': 75149312,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'spider_exceptions/SelectorSyntaxError': 1,
 'spider_exceptions/count': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 1, 41, 58274, tzinfo=datetime.timezone.utc)}
2025-06-13 15:01:41 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:02:18 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:02:18 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:02:18 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:02:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:02:18 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:02:18 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:02:18 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:02:18 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:02:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:02:18 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:02:18 [scrapy.middleware] INFO: Enabled item pipelines:
[]
2025-06-13 15:02:18 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:02:18 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:02:18 [market] INFO: Spider opened: market
2025-06-13 15:02:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:02:18 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 15:02:18 [scrapy.core.scraper] ERROR: Spider error processing <GET https://www.mql5.com/en/market/mt5> (referer: None)
Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/defer.py", line 343, in iter_errback
    yield next(it)
          ^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/middlewares.py", line 17, in process_spider_output
    for i in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/depth.py", line 59, in process_spider_output
    yield from super().process_spider_output(response, result, spider)
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 63, in parse
    item['price'] = self.extract_product_price_near_link(product_link)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 133, in extract_product_price_near_link
    price_elements = container.css(selector).getall()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in css
    return self.__class__(flatten([x.css(query) for x in self]))
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in <listcomp>
    return self.__class__(flatten([x.css(query) for x in self]))
                                   ^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 655, in css
    return self.xpath(self._css2xpath(query))
                      ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 659, in _css2xpath
    return _ctgroup[type]["_csstranslator"].css_to_xpath(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/csstranslator.py", line 137, in css_to_xpath
    return super().css_to_xpath(css, prefix)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/xpath.py", line 223, in css_to_xpath
    for selector in parse(css)
                    ^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 529, in parse
    return list(parse_selector_group(stream))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 544, in parse_selector_group
    yield Selector(*parse_selector(stream))
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 553, in parse_selector
    result, pseudo_element = parse_simple_selector(stream)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 620, in parse_simple_selector
    result = parse_attrib(result, stream)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 786, in parse_attrib
    raise SelectorSyntaxError(f"Operator expected, got {next}")
cssselect.parser.SelectorSyntaxError: Operator expected, got <DELIM '(' at 10>
2025-06-13 15:02:18 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:02:18 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 310,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.164396,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 2, 18, 357416, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'items_per_minute': None,
 'log_count/ERROR': 1,
 'log_count/INFO': 10,
 'log_count/WARNING': 4,
 'memusage/max': 75276288,
 'memusage/startup': 75276288,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'spider_exceptions/SelectorSyntaxError': 1,
 'spider_exceptions/count': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 2, 18, 193020, tzinfo=datetime.timezone.utc)}
2025-06-13 15:02:18 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:02:34 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:02:34 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:02:34 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:02:34 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:02:34 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.feedexport.FeedExporter',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:02:34 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:02:34 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:02:34 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:02:34 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:02:34 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:02:34 [scrapy.middleware] INFO: Enabled item pipelines:
[]
2025-06-13 15:02:34 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:02:34 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:02:34 [market] INFO: Spider opened: market
2025-06-13 15:02:34 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:02:34 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 15:02:34 [scrapy.core.scraper] ERROR: Spider error processing <GET https://www.mql5.com/en/market/mt5> (referer: None)
Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/defer.py", line 343, in iter_errback
    yield next(it)
          ^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/middlewares.py", line 17, in process_spider_output
    for i in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/depth.py", line 59, in process_spider_output
    yield from super().process_spider_output(response, result, spider)
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 63, in parse
    item['price'] = self.extract_product_price_near_link(product_link)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 133, in extract_product_price_near_link
    price_elements = container.css(selector).getall()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in css
    return self.__class__(flatten([x.css(query) for x in self]))
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in <listcomp>
    return self.__class__(flatten([x.css(query) for x in self]))
                                   ^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 655, in css
    return self.xpath(self._css2xpath(query))
                      ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 659, in _css2xpath
    return _ctgroup[type]["_csstranslator"].css_to_xpath(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/csstranslator.py", line 137, in css_to_xpath
    return super().css_to_xpath(css, prefix)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/xpath.py", line 223, in css_to_xpath
    for selector in parse(css)
                    ^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 529, in parse
    return list(parse_selector_group(stream))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 544, in parse_selector_group
    yield Selector(*parse_selector(stream))
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 553, in parse_selector
    result, pseudo_element = parse_simple_selector(stream)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 620, in parse_simple_selector
    result = parse_attrib(result, stream)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 786, in parse_attrib
    raise SelectorSyntaxError(f"Operator expected, got {next}")
cssselect.parser.SelectorSyntaxError: Operator expected, got <DELIM '(' at 10>
2025-06-13 15:02:34 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:02:34 [scrapy.extensions.feedexport] INFO: Stored json feed (0 items) in: test_output.json
2025-06-13 15:02:34 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 296,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.165715,
 'feedexport/success_count/FileFeedStorage': 1,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 2, 34, 432619, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'items_per_minute': None,
 'log_count/ERROR': 1,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 75517952,
 'memusage/startup': 75517952,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'spider_exceptions/SelectorSyntaxError': 1,
 'spider_exceptions/count': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 2, 34, 266904, tzinfo=datetime.timezone.utc)}
2025-06-13 15:02:34 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:04:45 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:04:45 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:04:45 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:04:45 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:04:45 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:04:45 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:04:45 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:04:45 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:04:45 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:04:45 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:04:45 [scrapy.middleware] INFO: Enabled item pipelines:
[]
2025-06-13 15:04:45 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:04:45 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:04:45 [market] INFO: Spider opened: market
2025-06-13 15:04:45 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:04:45 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 15:04:45 [scrapy.core.scraper] ERROR: Spider error processing <GET https://www.mql5.com/en/market/mt5> (referer: None)
Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/defer.py", line 343, in iter_errback
    yield next(it)
          ^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/middlewares.py", line 17, in process_spider_output
    for i in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/depth.py", line 59, in process_spider_output
    yield from super().process_spider_output(response, result, spider)
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 67, in parse
    item['price'] = self.extract_product_price_near_link(product_link)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 140, in extract_product_price_near_link
    price_elements = container.css(selector).getall()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in css
    return self.__class__(flatten([x.css(query) for x in self]))
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in <listcomp>
    return self.__class__(flatten([x.css(query) for x in self]))
                                   ^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 655, in css
    return self.xpath(self._css2xpath(query))
                      ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 659, in _css2xpath
    return _ctgroup[type]["_csstranslator"].css_to_xpath(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/csstranslator.py", line 137, in css_to_xpath
    return super().css_to_xpath(css, prefix)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/xpath.py", line 223, in css_to_xpath
    for selector in parse(css)
                    ^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 529, in parse
    return list(parse_selector_group(stream))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 544, in parse_selector_group
    yield Selector(*parse_selector(stream))
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 553, in parse_selector
    result, pseudo_element = parse_simple_selector(stream)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 620, in parse_simple_selector
    result = parse_attrib(result, stream)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 786, in parse_attrib
    raise SelectorSyntaxError(f"Operator expected, got {next}")
cssselect.parser.SelectorSyntaxError: Operator expected, got <DELIM '(' at 10>
2025-06-13 15:04:45 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:04:45 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 310,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.160576,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 4, 45, 441670, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'items_per_minute': None,
 'log_count/ERROR': 1,
 'log_count/INFO': 10,
 'log_count/WARNING': 4,
 'memusage/max': 75317248,
 'memusage/startup': 75317248,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'spider_exceptions/SelectorSyntaxError': 1,
 'spider_exceptions/count': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 4, 45, 281094, tzinfo=datetime.timezone.utc)}
2025-06-13 15:04:45 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:06:18 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:06:18 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:06:18 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:06:18 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:06:18 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:06:18 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:06:19 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:06:19 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:06:19 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:06:19 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:06:19 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:06:19 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:06:19 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:06:19 [market] INFO: Spider opened: market
2025-06-13 15:06:19 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:06:19 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 15:06:19 [scrapy.core.scraper] ERROR: Spider error processing <GET https://www.mql5.com/en/market/mt5> (referer: None)
Traceback (most recent call last):
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/defer.py", line 343, in iter_errback
    yield next(it)
          ^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/python.py", line 369, in __next__
    return next(self.data)
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/middlewares.py", line 17, in process_spider_output
    for i in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/depth.py", line 59, in process_spider_output
    yield from super().process_spider_output(response, result, spider)
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py", line 58, in process_spider_output
    for o in result:
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py", line 167, in process_sync
    yield from iterable
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 67, in parse
    item['price'] = self.extract_product_price_near_link(product_link)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/src/voyagr_scrapy/spiders/market_spider.py", line 140, in extract_product_price_near_link
    price_elements = container.css(selector).getall()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in css
    return self.__class__(flatten([x.css(query) for x in self]))
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 191, in <listcomp>
    return self.__class__(flatten([x.css(query) for x in self]))
                                   ^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 655, in css
    return self.xpath(self._css2xpath(query))
                      ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/selector.py", line 659, in _css2xpath
    return _ctgroup[type]["_csstranslator"].css_to_xpath(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/parsel/csstranslator.py", line 137, in css_to_xpath
    return super().css_to_xpath(css, prefix)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/xpath.py", line 223, in css_to_xpath
    for selector in parse(css)
                    ^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 529, in parse
    return list(parse_selector_group(stream))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 544, in parse_selector_group
    yield Selector(*parse_selector(stream))
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 553, in parse_selector
    result, pseudo_element = parse_simple_selector(stream)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 620, in parse_simple_selector
    result = parse_attrib(result, stream)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/cssselect/parser.py", line 786, in parse_attrib
    raise SelectorSyntaxError(f"Operator expected, got {next}")
cssselect.parser.SelectorSyntaxError: Operator expected, got <DELIM '(' at 10>
2025-06-13 15:06:19 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:06:19 [market] INFO: Exported 0 items to output/market_20250613_150619.json
2025-06-13 15:06:19 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 306,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.165644,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 6, 19, 258886, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'items_per_minute': None,
 'log_count/ERROR': 1,
 'log_count/INFO': 11,
 'log_count/WARNING': 4,
 'memusage/max': 74948608,
 'memusage/startup': 74948608,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'spider_exceptions/SelectorSyntaxError': 1,
 'spider_exceptions/count': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 6, 19, 93242, tzinfo=datetime.timezone.utc)}
2025-06-13 15:06:19 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:08:44 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:08:44 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:08:44 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:08:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:08:44 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:08:44 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:08:44 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:08:44 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:08:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:08:44 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:08:44 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:08:44 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:08:44 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:08:44 [market] INFO: Spider opened: market
2025-06-13 15:08:44 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:08:44 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 15:08:44 [market] INFO: Extracted item for product 118805: name='Quantum Queen MT5', price=1199.99
2025-06-13 15:08:44 [market] INFO: Extracted item for product 137361: name='Swing Master EA', price=999.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 103452: name='Quantum Emperor MT5', price=1149.99
2025-06-13 15:08:44 [market] INFO: Extracted item for product 127013: name='Quantum Bitcoin EA', price=899.99
2025-06-13 15:08:44 [market] INFO: Extracted item for product 137760: name='GbpUsd Commander', price=499.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '1 copies left at $999 Next price $1099 Let me introduce you '
                'to an Expert Advisor, built on the foundation of my manual '
                'trading system — Algo Pumping .\xa0I seriously upgraded this '
                'strat, loaded it with key tweaks, filters, and tech hacks, '
                'and now I’m dropping a trading bot that: Crushes the markets '
                'with the advanced Algo Pumping Swing Trading algorithm, Slaps '
                'Stop Loss orders to protect your account, Perfectly fits both '
                '"Prop Firm Trading" and "Personal Trading", Trades clean '
                'without martingale or',
 'images': ['https://c.mql5.com/31/1367/swing-master-ea-logo-200x200-6523.png'],
 'market_id': 'mt5',
 'name': 'Swing Master EA',
 'price': 999.0,
 'product_id': '137361',
 'rating': 5.0,
 'reviews_count': 18,
 'scraped_at': '2025-06-13T15:08:44.457285',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing \xa0 Quantum Emperor EA , the groundbreaking MQL5 '
                "expert advisor that's transforming the way you trade the "
                'prestigious GBPUSD pair! Developed by a team of experienced '
                'traders with trading experience of over 13 years. IMPORTANT! '
                'After the purchase please send me a private message to '
                'receive the installation manual and the setup instructions. '
                '***Buy Quantum Emperor EA and you could get Quantum StarMan '
                'for free !*** Ask in private for more details Live Signal V5: '
                'Click Here \n'
                '\n'
                'MT4 Version :',
 'images': ['https://c.mql5.com/31/1404/quantum-emperor-mt5-logo-200x200-3721.png'],
 'market_id': 'mt5',
 'name': 'Quantum Emperor MT5',
 'price': 1149.99,
 'product_id': '103452',
 'rating': 4.86,
 'reviews_count': 414,
 'scraped_at': '2025-06-13T15:08:44.462223',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 130426: name='Beatrix Inventor MT5', price=1499.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 134329: name='AiQ', price=1837.97
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Quantum Bitcoin EA : There is no such thing as impossible, '
                "it's only a matter of figuring out how to do it! \n"
                'Step into the future of Bitcoin trading with Quantum Bitcoin '
                'EA , the latest masterpiece from one of the top MQL5 sellers. '
                'Designed for traders who demand performance, precision, and '
                "stability, Quantum Bitcoin redefines what's possible in the "
                'volatile world of cryptocurrency. \n'
                'IMPORTANT! After the purchase please send me a private '
                'message to receive the installation manual and the setup i',
 'images': ['https://c.mql5.com/31/1404/quantum-bitcoin-ea-logo-200x200-3009.png'],
 'market_id': 'mt5',
 'name': 'Quantum Bitcoin EA',
 'price': 899.99,
 'product_id': '127013',
 'rating': 5.0,
 'reviews_count': 73,
 'scraped_at': '2025-06-13T15:08:44.469188',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'GBPUSD Commander – Structured Scalping on M30-(lowest risk in '
                'the world) GBPUSD Commander is an Expert Advisor designed '
                'specifically for GBP/USD on the 30-minute timeframe. It '
                'applies structured technical logic with fixed Stop Loss and '
                'Take Profit levels, adaptive lot sizing, and risk control '
                'built into each position. The EA avoids the use of '
                'martingale, grid, or hedge techniques and operates with '
                'clearly defined trade logic for consistent execution. '
                'Suitable for both newer traders and experienc',
 'images': ['https://c.mql5.com/31/1415/gbpusd-commander-logo-200x200-1108.png'],
 'market_id': 'mt5',
 'name': 'GbpUsd Commander',
 'price': 499.0,
 'product_id': '137760',
 'rating': 5.0,
 'reviews_count': 10,
 'scraped_at': '2025-06-13T15:08:44.475074',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ibrahim Aljaref'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 125754: name='Monic', price=399.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 23415: name='Trade Assistant MT5', price=100.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 139787: name='FastWay EA', price=1287.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing my new Expert Advisor Beatrix Inventor, Beatrix '
                'Inventor EA uses the concept of following trends in '
                'conducting market analysis. Analyzing market trends with the '
                'main indicators Bollinger Band and Moving Average, when '
                'entering transactions, this EA also considers the Orderblock '
                'zone which makes the analysis more accurate. The algorithm '
                'used in developing this EA is a reliable algorithm both in '
                'entry and managing floating minus.\n'
                '\n'
                'This EA is designed to be used on the XAUUSD / GOLD pair',
 'images': ['https://c.mql5.com/31/1402/beatrix-inventor-mt5-logo-200x200-9075.png'],
 'market_id': 'mt5',
 'name': 'Beatrix Inventor MT5',
 'price': 1499.0,
 'product_id': '130426',
 'rating': 3.62,
 'reviews_count': 91,
 'scraped_at': '2025-06-13T15:08:44.481566',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Azil Al Azizul'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing AIQ Version 2.3—The Evolution of Autonomous '
                "Trading Intelligence I'm proud to present AIQ (Autonomous "
                'Intelligence), the next generation of AI-powered trading '
                'technology. Version 2.3 now features AI Web Search and '
                'Enhanced APM (AI Position Management) - AIQ can search the '
                'internet in real-time for breaking news, political events, '
                'interest rates, and market sentiment, while the upgraded APM '
                'manages your Take Profit and Stop Loss like a professional '
                'trader in real-time. Building on th',
 'images': ['https://c.mql5.com/31/1399/aiq-logo-200x200-1013.png'],
 'market_id': 'mt5',
 'name': 'AiQ',
 'price': 1837.97,
 'product_id': '134329',
 'rating': 4.83,
 'reviews_count': 29,
 'scraped_at': '2025-06-13T15:08:44.485810',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 133653: name='Ultimate Breakout System', price=999.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 135273: name='Burning Grid', price=999.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 134465: name='AI Neuro Dynamics MT5', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 124990: name='King Sniper EA', price=599.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Live signal \n'
                '\n'
                'Find out more here: \xa0 '
                'https://www.mql5.com/en/users/prizmal/seller \n'
                'The strategy uses an averaging trading approach, relying on '
                'the Stochastic Oscillator and Bollinger Bands as the main '
                'indicators. It consistently implements dynamic take-profit '
                'and stop-loss levels for each trade. Optimization was '
                'conducted using 14 years of data (from 2010 to 2024) on the '
                'IC Markets server with a Standard account type. \n'
                '\n'
                'Recommendations: \n'
                'Currency Pair: AUDCAD Minimum Deposit: $500 USD Account: H',
 'images': ['https://c.mql5.com/31/1418/monic-logo-200x200-3269.png'],
 'market_id': 'mt5',
 'name': 'Monic',
 'price': 399.0,
 'product_id': '125754',
 'rating': 4.56,
 'reviews_count': 9,
 'scraped_at': '2025-06-13T15:08:44.497071',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladimir Lekhovitser'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'It helps to calculate the risk per trade, the easy '
                'installation of a new order, order management with partial '
                'closing functions, trailing stop of 7 types and other useful '
                'functions. \n'
                'Additional materials and instructions\n'
                'Installation instructions - Application instructions - Trial '
                'version of the application for a demo account \n'
                'Line function - \xa0 shows on the chart the Opening line, '
                'Stop Loss, Take Profit. With this function it is easy to set '
                'a new order and see its additional characteristics bef',
 'images': ['https://c.mql5.com/31/1243/trade-assistant-mt5-logo-200x200-4863.png'],
 'market_id': 'mt5',
 'name': 'Trade Assistant MT5',
 'price': 100.0,
 'product_id': '23415',
 'rating': 4.44,
 'reviews_count': 178,
 'scraped_at': '2025-06-13T15:08:44.500686',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Evgeniy Kravchenko'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'FastWay EA is a smart and efficient automated trading system '
                'built on a powerful mean-reversion strategy. It focuses on '
                'trading correlated currency pairs like AUDCAD, AUDNZD, '
                'NZDCAD, and EURGBP , capitalizing on price returning to its '
                'average after strong directional moves.\n'
                'After purchase, please send a private message to receive full '
                'setup instructions. \n'
                'Live Signal:\xa0 CLICK HERE \n'
                '\n'
                'Post-launch offer: \xa0 Regular price is $1487 , but now '
                'FastWay EA is available at a discount — only $1287 for the n',
 'images': ['https://c.mql5.com/31/1402/fastway-ea-logo-200x200-7424.png'],
 'market_id': 'mt5',
 'name': 'FastWay EA',
 'price': 1287.0,
 'product_id': '139787',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:08:44.504550',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'PAVEL UDOVICHENKO'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 92199: name='Big Forex Players MT5', price=1999.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 127704: name='Scalping Robot MT5', price=899.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 139303: name='Scalper Investor', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 136033: name='Divergence Bomber', price=99.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'IMPORTANT : This package will only be sold at current price '
                'for a very limited number of copies. \xa0\xa0 Price will go '
                'to 1499$ very fast \xa0\xa0 +100 Strategies included and more '
                'coming! \n'
                'BONUS : At 999$ or higher price --> choose 5 \xa0of my other '
                "EA's for free!\xa0 ALL SET FILES COMPLETE SETUP AND "
                'OPTIMIZATION GUIDE VIDEO GUIDE LIVE SIGNALS REVIEW (3rd '
                'party) \n'
                "Welcome to the ULTIMATE BREAKOUT SYSTEM! I'm pleased to "
                'present the Ultimate Breakout System, a sophisticated and '
                'proprietary Expert Advisor (EA) met',
 'images': ['https://c.mql5.com/31/1420/ultimate-breakout-system-logo-200x200-3928.png'],
 'market_id': 'mt5',
 'name': 'Ultimate Breakout System',
 'price': 999.0,
 'product_id': '133653',
 'rating': 5.0,
 'reviews_count': 12,
 'scraped_at': '2025-06-13T15:08:44.514406',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Profalgo Limited'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Burning Grid EA MT5 – Multi-Pair Grid Power with Adaptive '
                'Risk \n'
                '\n'
                'Trade up to 35 forex pairs simultaneously with intelligent '
                'strategy selection, flexible risk profiles, and dynamic '
                'drawdown control.\n'
                'Actual Price: $999.00 - increases by with next 15 purchases '
                '(Next Price: $1199, Final price: $1999) Contact me to receive '
                'a time-limited, fully functional trial version! \n'
                'Manual: '
                'https://magma-software.solutions/burning-grid/bgmanual-en.html '
                'Community : https://www.mql5.com/en/messages/0151274c579fdb0',
 'images': ['https://c.mql5.com/31/1416/burning-grid-logo-200x200-1710.png'],
 'market_id': 'mt5',
 'name': 'Burning Grid',
 'price': 999.0,
 'product_id': '135273',
 'rating': 4.91,
 'reviews_count': 11,
 'scraped_at': '2025-06-13T15:08:44.518214',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Magma Software Solutions UG'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AI Neuro Dynamics EA Adaptive Signal Architecture for XAU/USD '
                '| H1 AI Neuro Dynamics is more than just an Expert Advisor — '
                'it is a modular cognitive trading system built for precision '
                'and adaptability on the XAU/USD (Gold) pair. Designed for '
                'high-volatility environments, it fully complies with the '
                'performance and risk requirements of prop firm standards. '
                'Powered by a proprietary neuro-quantum decision architecture '
                ', the EA evaluates market structure in real time, dynamically '
                'adjusting its inter',
 'images': ['https://c.mql5.com/31/1376/ai-neuro-dynamics-mt5-logo-200x200-8040.png'],
 'market_id': 'mt5',
 'name': 'AI Neuro Dynamics MT5',
 'price': 599.0,
 'product_id': '134465',
 'rating': 5.0,
 'reviews_count': 6,
 'scraped_at': '2025-06-13T15:08:44.521244',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Peter Robert Grange'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Monitoring of real trading Advisor -\xa0 '
                'https://www.mql5.com/en/signals/2264971 My other '
                'products\xa0 \xa0 -\xa0 \xa0 \xa0 click here Keep in mind '
                'that the results on different brokers may differ, I recommend '
                'testing on your broker before using it\xa0(you can ask me for '
                'a list of recommended brokers in the PM). Read the blog post '
                'with the description of the adviser before starting work and '
                'if you have any additional questions, write to me in the PM. '
                'A fully automatic Expert Advisor that does not require '
                'additional',
 'images': ['https://c.mql5.com/31/1206/king-sniper-ea-logo-200x200-7179.png'],
 'market_id': 'mt5',
 'name': 'King Sniper EA',
 'price': 599.0,
 'product_id': '124990',
 'rating': 5.0,
 'reviews_count': 9,
 'scraped_at': '2025-06-13T15:08:44.523659',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ivan Bebikov'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 52212: name='Bonnitta EA MT5', price=5500.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 132932: name='GoldenHour', price=239.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 123850: name='GbpUsd Robot MT5', price=499.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 133197: name='AI Gold Sniper MT5', price=399.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'We proudly present our cutting-edge robot, the\xa0 Big Forex '
                'Players EA \xa0designed to maximize your trading potential, '
                'minimize emotional trading, and make smarter decisions '
                'powered by cutting-edge technology. The whole system in this '
                'EA took us many months to build, and then we spent a lot of '
                'time testing it.\xa0This unique EA includes three distinct '
                'strategies that can be used independently or in together.\xa0'
                'The robot receives the positions of the\xa0 biggest\xa0'
                'Banks \xa0(positions are sent from our database t',
 'images': ['https://c.mql5.com/31/1042/big-forex-players-mt5-logo-200x200-3102.png'],
 'market_id': 'mt5',
 'name': 'Big Forex Players MT5',
 'price': 1999.0,
 'product_id': '92199',
 'rating': 4.7,
 'reviews_count': 100,
 'scraped_at': '2025-06-13T15:08:44.529008',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing our advanced Scalping Forex Robot. The scalping '
                'algorithm is built to spot high-probability entry and exit '
                'points, ensuring that every trade is executed with the '
                'highest chance of success within the M1 timeframe . The best '
                'pair to use with the Scalping Robot is XAUUSD .This robot is '
                'perfect for traders who prefer the scalping method and want '
                'to take advantage of rapid price movements without having to '
                'manually monitor the charts. It is suitable for both '
                'beginners looking for an autom',
 'images': ['https://c.mql5.com/31/1243/scalping-robot-mt5-logo-200x200-4701.png'],
 'market_id': 'mt5',
 'name': 'Scalping Robot MT5',
 'price': 899.0,
 'product_id': '127704',
 'rating': 4.37,
 'reviews_count': 65,
 'scraped_at': '2025-06-13T15:08:44.531844',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': "9 copies left at $599 Next price $699 Hey traders, If you're "
                "looking for an EA that doesn't just fire off trades for the "
                'sake of activity, but actually follows a smart, battle-tested '
                'strategy — meet Scalper Investor EA. This is a multi-currency '
                'expert advisor already armed with a solid reversal strategy, '
                'and soon to be upgraded with a trend-following module. Ready '
                'to trade: The Reversal Strategy\n'
                'At launch, Scalper Investor EA comes fully loaded with a '
                'reversal system designed to catch pullbacks',
 'images': ['https://c.mql5.com/31/1395/scalper-investor-logo-200x200-6238.png'],
 'market_id': 'mt5',
 'name': 'Scalper Investor',
 'price': 599.0,
 'product_id': '139303',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:08:44.534746',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'Each buyer of this indicator also receives the following for '
                'free:\n'
                'The custom utility "Bomber Utility", which automatically '
                'manages every trade, sets Stop Loss and Take Profit levels, '
                'and closes trades according to the rules of this strategy Set '
                'files for configuring the indicator for various assets Set '
                'files for configuring Bomber Utility in the following modes: '
                '"Minimum Risk", "Balanced Risk", and "Wait-and-See Strategy" '
                'A step-by-step video manual to help you quickly install, '
                'configure, and s',
 'images': ['https://c.mql5.com/31/1354/divergence-bomber-logo-200x200-2956.png'],
 'market_id': 'mt5',
 'name': 'Divergence Bomber',
 'price': 99.0,
 'product_id': '136033',
 'rating': 5.0,
 'reviews_count': 34,
 'scraped_at': '2025-06-13T15:08:44.537221',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 123089: name='Aura Neuron MT5', price=1000.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 138998: name='GoldXpert MT5', price=250.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 113589: name='Way To Stars MT5', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 124335: name='The Gold Phoenix', price=1397.67
2025-06-13 15:08:44 [market] INFO: Extracted item for product 64961: name='Aura Black Edition MT5', price=1500.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Bonnitta EA \xa0is based on Pending Position strategy ( PPS ) '
                'and a very advanced secretive trading algorithm. The strategy '
                'of\xa0 Bonnitta EA \xa0is a combination of a secretive custom '
                'indicator, Trendlines, Support & Resistance levels ( Price '
                'Action ) and most important secretive trading algorithm '
                "mentioned above. DON'T BUY AN EA WITHOUT ANY REAL MONEY TEST "
                'OF MORE THAN 3 MONTHS, IT TOOK ME MORE THAN 100 WEEKS(MORE '
                'THAN 2 YEARS) TO TEST BONNITTA EA ON REAL MONEY AND SEE THE '
                'RESULT ON THE LINK BELOW. B',
 'images': ['https://c.mql5.com/31/825/bonnitta-ea-mt5-logo-200x200-9279.png'],
 'market_id': 'mt5',
 'name': 'Bonnitta EA MT5',
 'price': 5500.0,
 'product_id': '52212',
 'rating': 3.33,
 'reviews_count': 21,
 'scraped_at': '2025-06-13T15:08:44.542705',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ugochukwu Mobi'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'GoldenHour Expert Advisor\xa0 A precision scalping system for '
                'XAUUSD (Gold) that focuses on high-probability '
                'single-position trades. The EA executes 2-3 carefully '
                'selected trades per day during optimal market conditions, '
                'avoiding risky multi-position or martingale strategies. \n'
                'NEXT price 599$\xa0\xa0 " If the EA doesn\'t perform well in '
                'your backtest, please feel free to message me. I’ll be happy '
                'to help you set it up correctly and get the best possible '
                'results ." \n'
                'Trading Approach: - Single position tra',
 'images': ['https://c.mql5.com/31/1383/goldenhour-logo-200x200-9546.png'],
 'market_id': 'mt5',
 'name': 'GoldenHour',
 'price': 239.0,
 'product_id': '132932',
 'rating': 3.33,
 'reviews_count': 6,
 'scraped_at': '2025-06-13T15:08:44.546026',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Zaha Feiz'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The GBPUSD Robot MT5 is an advanced automated trading system '
                'meticulously designed for the specific dynamics of the \xa0 '
                'GBP/USD \xa0 currency pair. Utilizing advanced technical '
                'analysis, the robot assesses historical and real-time data '
                'to \xa0 identify potential trends , key support and '
                'resistance levels, and other relevant market signals specific '
                'to GBP/USD.\xa0 The Robot opens positions\xa0 every day,\xa0 '
                'from Monday to Friday, and\xa0 all positions are secured \xa0'
                'with Take Profit, Stop Loss, Trailing Stop, Break-E',
 'images': ['https://c.mql5.com/31/1200/gbpusd-robot-mt5-logo-200x200-8029.png'],
 'market_id': 'mt5',
 'name': 'GbpUsd Robot MT5',
 'price': 499.0,
 'product_id': '123850',
 'rating': 4.69,
 'reviews_count': 125,
 'scraped_at': '2025-06-13T15:08:44.548664',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Forex EA Trading Channel on MQL5:\xa0 Join my MQL5 channel to '
                'update the latest news from me.\xa0 My community of over '
                '14,000 members on MQL5 . \n'
                'ONLY 3 COPIES OUT OF 10 LEFT AT $399! After that, the price '
                'will be raised to $499.\n'
                '\n'
                '- REAL SIGNAL\xa0 Low Risk:\xa0 '
                'https://www.mql5.com/en/signals/2302784 \n'
                'IC Markets - High Risk: \xa0 '
                'https://www.mql5.com/en/signals/2310008 \n'
                'Full installation instructions for EA AI Gold Sniper to work '
                'properly are updated at \xa0 comment #3 \n'
                '\n'
                'AI Gold Sniper applies the latest GPT-4o',
 'images': ['https://c.mql5.com/31/1358/ai-gold-scalper-mt5-logo-200x200-8759.png'],
 'market_id': 'mt5',
 'name': 'AI Gold Sniper MT5',
 'price': 399.0,
 'product_id': '133197',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:08:44.551883',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ho Tuan Thang'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 133492: name='Apex Flow', price=668.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 135571: name='Venom Gold Pro', price=393.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 139184: name='Risk Killer AI MT5', price=399.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 139373: name='AlphaCore X', price=490.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Neuron is a distinctive Expert Advisor that continues '
                'the Aura series of trading systems. By leveraging advanced '
                'Neural Networks and cutting-edge classic trading strategies, '
                'Aura Neuron offers an innovative approach with excellent '
                'potential performance. Fully automated, this Expert Advisor '
                'is designed to trade currency pairs such as XAUUSD (GOLD). It '
                'has demonstrated consistent stability across these pairs from '
                '1999 to 2023. The system avoids dangerous money management '
                'techniques, such as m',
 'images': ['https://c.mql5.com/31/1359/aura-neuron-mt5-logo-200x200-4436.png'],
 'market_id': 'mt5',
 'name': 'Aura Neuron MT5',
 'price': 1000.0,
 'product_id': '123089',
 'rating': 4.87,
 'reviews_count': 38,
 'scraped_at': '2025-06-13T15:08:44.556705',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'By popular demand from my large Xpert community (15,000+ '
                'downloads) , I’ve developed a fully automated Expert Advisor '
                'for the highly volatile gold market (XAUUSD) – a powerful '
                'solution for traders looking to capitalize on breakouts, '
                'follow trends, and utilize multiple signals per week . '
                'GoldXpert is designed specifically for beginners and '
                'semi-professionals , offering precise analysis to optimize '
                'your trading strategy and targeting those who want to benefit '
                'from dynamic market movements . Built',
 'images': ['https://c.mql5.com/31/1392/goldxpert-mt5-logo-200x200-1249.png'],
 'market_id': 'mt5',
 'name': 'GoldXpert MT5',
 'price': 250.0,
 'product_id': '138998',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:08:44.559197',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Steve Rosenstock'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Way To Stars is an automated trading system based on the '
                'classic night scalping logic, designed to capture short-term '
                'opportunities during the lowest volatility periods of the '
                'market. \n'
                'Nighttime trading tends to have lower noise and weaker '
                'trends, making it suitable for high-frequency and precise '
                'operations. This type of strategy has existed in the field of '
                'algorithmic trading for over two decades. Way To Stars '
                'inherits this mature framework and rebuilds its algorithm to '
                'fully adapt to current',
 'images': ['https://c.mql5.com/31/1093/way-to-stars-mt5-logo-200x200-9461.png'],
 'market_id': 'mt5',
 'name': 'Way To Stars MT5',
 'price': 599.0,
 'product_id': '113589',
 'rating': 4.73,
 'reviews_count': 30,
 'scraped_at': '2025-06-13T15:08:44.561500',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Wei Tu'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Gold Phoenix GPT - The Ultimate AI Trading Tool for Gold '
                'Pairs Introducing Gold Phoenix GPT, the most thorough and '
                'honest implementation of AI for gold trading. Built on '
                'real-world performance, this EA is specifically designed for '
                'gold pairs, using a powerful breakout strategy on the M1 '
                'timeframe. Unlike many tools that perform well in backtests '
                'but fall short in live conditions, Gold Phoenix GPT excels '
                'where it matters most: in fast-moving live gold markets. '
                'Powered by advanced AI—including Cha',
 'images': ['https://c.mql5.com/31/1219/the-gold-phoenix-g-p-t-logo-200x200-9392.png'],
 'market_id': 'mt5',
 'name': 'The Gold Phoenix',
 'price': 1397.67,
 'product_id': '124335',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:08:44.564197',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Black Edition\xa0is a fully automated EA designed to '
                'trade\xa0GOLD\xa0only. Expert showed stable results on XAUUSD '
                'in 2011-2020 period. No dangerous methods of money management '
                'used, no martingale, no grid or scalp. Suitable for any '
                'broker conditions. EA trained with a\xa0multilayer '
                'perceptron\xa0Neural Network\xa0(MLP) is a class of\xa0'
                'feedforward\xa0artificial neural network\xa0(ANN). The term '
                'MLP is used ambiguously, sometimes loosely to\xa0any\xa0'
                'feedforward ANN, sometimes strictly to refer to networks '
                'composed of mult',
 'images': ['https://c.mql5.com/31/1386/aura-black-edition-mt5-logo-200x200-8408.png'],
 'market_id': 'mt5',
 'name': 'Aura Black Edition MT5',
 'price': 1500.0,
 'product_id': '64961',
 'rating': 4.46,
 'reviews_count': 37,
 'scraped_at': '2025-06-13T15:08:44.566407',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 128620: name='Bitcoin Robot Grid MT5', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 131124: name='Golden Algo', price=399.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 138544: name='ScalpPrime', price=499.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 135582: name='Aura Bitcoin Hash', price=675.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '【突破性US30交易策略】 '
                '这款EA专为US30指数（道琼斯工业平均指数）量身定制，融合先进的时间管理与动态风控技术，为您打造前瞻性且精准高效的交易体验。 '
                '为不同经验水平的交易者提供简便高效的交易体验，并支持个性化设置，满足多样化需求 ，在这里都能找到符合自己需求的操作模式。 '
                '如需查看该策略的交易表现: Live account signal \n'
                '\n'
                '专属资产优化 ：专为US30设计，精准捕捉市场脉动，不适用于其他货币对或资产。 灵活时间框架 '
                '：适用于任意时间框架，随时随地灵活操作。 个性化止损与止盈 '
                '：根据您的风控需求，自定义设置止损、止盈及追踪止损功能，确保风险与收益达到最佳平衡。 动态仓位管理 '
                '：支持固定手数与动态手数分配，满足不同账户规模的需求，提升资金效率。 小资金友好 '
                '：即使是较小资金账户也可顺利运行，体验专业级交易。 平台兼容推荐 ：建议使用对冲账户模式与高流动性平台（例如IC '
                'Market），并推荐在H1时间框架运行 。',
 'images': ['https://c.mql5.com/31/1318/apex-flow-logo-200x200-9015.png'],
 'market_id': 'mt5',
 'name': 'Apex Flow',
 'price': 668.0,
 'product_id': '133492',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:08:44.571269',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dong Zhi Sun'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Venom Gold Pro: Automated XAUUSD M30 EA Venom Gold Pro is a '
                "fully automated Expert Advisor. It's built for XAUUSD (Gold) "
                'on the M30 timeframe . This system uses advanced, pure price '
                'action logic. It monitors market behavior and executes trades '
                'in real-time. No indicators or martingale methods are used. '
                'We focus on clean, rule-based execution. Venom Gold Pro '
                'offers four proven, high-win-rate strategies . You can select '
                'the style that best fits your trading approach. Key Features '
                'Automated Tradin',
 'images': ['https://c.mql5.com/31/1418/venom-gold-pro-logo-200x200-1559.png'],
 'market_id': 'mt5',
 'name': 'Venom Gold Pro',
 'price': 393.0,
 'product_id': '135571',
 'rating': 4.38,
 'reviews_count': 29,
 'scraped_at': '2025-06-13T15:08:44.574091',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antoine Melhem'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Generate consistent returns with a Grok3 AI-assisted , '
                'risk-diversified and Bitcoin-boosted EA . RiskKILLER AI is a '
                'breakout scalping algorithm identifying key levels for '
                'potential high-volatility moves, selecting best risk-reward '
                'trades while diversifying risk on 5 assets. $399: 4 copies '
                'left\xa0 / 10 . After purchase, to get the API key and the '
                'User Manual, 1. post a comment asking for them 2. mail me '
                'directly (mail findable in the dedicated group - see below). '
                '[ Live Signal ] - [\xa0 Specs & Set-up',
 'images': ['https://c.mql5.com/31/1420/risk-killer-ai-mt5-logo-200x200-7662.png'],
 'market_id': 'mt5',
 'name': 'Risk Killer AI MT5',
 'price': 399.0,
 'product_id': '139184',
 'rating': 5.0,
 'reviews_count': 10,
 'scraped_at': '2025-06-13T15:08:44.576782',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Christophe Pa Trouillas'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AlphaCore X The AlphaCore X EA is a cutting-edge trading '
                'system that masters the complexity of financial markets with '
                'a unique combination of AI-driven analyses and data-based '
                'algorithms. By integrating ChatGPT-o1 , the latest GPT-4.5 , '
                'advanced machine learning models, and a robust big data '
                'approach, AlphaCore X achieves a new level of precision, '
                'adaptability, and efficiency. This Expert Advisor impresses '
                'with its innovative strategy, seamless AI interaction, and '
                'comprehensive additional featu',
 'images': ['https://c.mql5.com/31/1396/alphacore-x-logo-200x200-2807.png'],
 'market_id': 'mt5',
 'name': 'AlphaCore X',
 'price': 490.0,
 'product_id': '139373',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:08:44.579129',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Arseny Potyekhin'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 54468: name='EA Gold Stuff mt5', price=50.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 138379: name='INFusion', price=476.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 107189: name='Quantum StarMan', price=499.99
2025-06-13 15:08:44 [market] INFO: Extracted item for product 138199: name='AI ZeroPoint Dynamics MT5', price=499.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Bitcoin Robot Grid MT5 is an intelligent trading system '
                'designed to automate BTCUSD trading using the grid trading '
                'strategy. This method takes advantage of market fluctuations '
                'by placing a structured series of buy and sell orders at '
                'predefined price levels. The robot continuously monitors '
                'market conditions and executes trades according to its preset '
                'parameters, allowing for consistent market engagement without '
                'the need for manual intervention. Bitcoin Robot Grid is the '
                'perfect solution for trad',
 'images': ['https://c.mql5.com/31/1256/bitcoin-robot-grid-mt5-logo-200x200-5077.png'],
 'market_id': 'mt5',
 'name': 'Bitcoin Robot Grid MT5',
 'price': 599.0,
 'product_id': '128620',
 'rating': 5.0,
 'reviews_count': 10,
 'scraped_at': '2025-06-13T15:08:44.585235',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Golden Algo\xa0– The Ultimate AI-Powered Expert Advisor for '
                'Gold Traders Golden Algo Expert Advisor is a powerful trading '
                'system designed specifically for XAUUSD (Gold). It combines '
                'technical indicators with real-time market data—including the '
                'US Index and market sentiment—to generate precise trade '
                'signals. Each signal is then filtered through an advanced '
                'OpenAI-powered process to ensure only high-probability trades '
                'are executed. By blending technical analysis, fundamental '
                'insights, and artificial',
 'images': ['https://c.mql5.com/31/1329/golden-algo-logo-200x200-4853.png'],
 'market_id': 'mt5',
 'name': 'Golden Algo',
 'price': 399.0,
 'product_id': '131124',
 'rating': 1.89,
 'reviews_count': 76,
 'scraped_at': '2025-06-13T15:08:44.588288',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ramethara Vijayanathan'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'ScalpPrime \xa0 EA \xa0 \n'
                'Advanced Dual-Strategy Expert Advisor for Structured and '
                'Rule-Based Gold Scalping \xa0 ScalpPrime \xa0 EA is a '
                'precision-engineered Expert Advisor tailored for trading '
                'XAUUSD (Gold) using short-term. It is built on a '
                'professional-grade, rule-based framework that combines two '
                'complementary approaches: a Fibonacci retracement strategy '
                'for \xa0 identifying \xa0 high-probability price zones and a '
                'volume-based confirmation system for filtering market noise '
                'and validating trade entries. \xa0 Desi',
 'images': ['https://c.mql5.com/31/1395/scalpprime-logo-200x200-6661.png'],
 'market_id': 'mt5',
 'name': 'ScalpPrime',
 'price': 499.0,
 'product_id': '138544',
 'rating': 2.88,
 'reviews_count': 25,
 'scraped_at': '2025-06-13T15:08:44.590725',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'DRT Circle'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Bitcoin Hash EA is a distinctive Expert Advisor that '
                'continues the Aura series of trading systems. By leveraging '
                'advanced Neural Networks and cutting-edge classic trading '
                'strategies, Aura BTC offers an innovative approach with '
                'excellent potential performance. Fully automated, this Expert '
                'Advisor is designed to trade currency pair BTCUSD (Bitcoin). '
                'It has demonstrated consistent stability across these pairs '
                'from 2017 to 2025. The system avoids dangerous money '
                'management techniques, such as m',
 'images': ['https://c.mql5.com/31/1347/aura-bitcoin-hash-logo-200x200-8237.png'],
 'market_id': 'mt5',
 'name': 'Aura Bitcoin Hash',
 'price': 675.0,
 'product_id': '135582',
 'rating': 4.93,
 'reviews_count': 14,
 'scraped_at': '2025-06-13T15:08:44.593688',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 92195: name='XG Gold Robot MT5', price=899.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 39150: name='Forex Trade Manager MT5', price=99.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 109036: name='Eternal Engine EA MT5', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 134062: name='Algo Pumping', price=99.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'EA Gold Stuff mt5\xa0is an Expert Advisor designed '
                'specifically for trading gold. The operation is based on '
                'opening orders using the \xa0Gold Stuff mt5 \xa0indicator, '
                'thus the EA works according to the "Trend Follow" strategy, '
                'which means following the trend. For Expert Advisor need '
                'hedge type account\xa0 Contact me immediately after the '
                'purchase to get personal bonus!\xa0 You can get a free copy '
                'of our Strong Support and Trend Scanner indicator, please pm. '
                'me! Settings\xa0 and manual \xa0 here\xa0 \n'
                'Please note that I',
 'images': ['https://c.mql5.com/31/1340/ea-gold-stuff-mt5-logo-200x200-7851.png'],
 'market_id': 'mt5',
 'name': 'EA Gold Stuff mt5',
 'price': 50.0,
 'product_id': '54468',
 'rating': 4.74,
 'reviews_count': 647,
 'scraped_at': '2025-06-13T15:08:44.599275',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vasiliy Strukov'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Automated Zone-Adaptive Impulse Logic and AI in an EA\n'
                'INFusion is a fully automated Expert Advisor that trades '
                'exclusively the gold market (XAU/USD). Designed for '
                'MetaTrader, the system uses current GPT models\xa0 GPT-o4 '
                'high to analyze price data in real time. The EA combines '
                'modern AI methods with a proprietary price logic, creating a '
                'trading approach that flexibly adapts to market behavior – '
                'without traditional indicators, without martingale, grid or '
                'news trading. INFusion \xa0 is aimed at users s',
 'images': ['https://c.mql5.com/31/1380/quantedgex-logo-200x200-8739.png'],
 'market_id': 'mt5',
 'name': 'INFusion',
 'price': 476.0,
 'product_id': '138379',
 'rating': 5.0,
 'reviews_count': 4,
 'scraped_at': '2025-06-13T15:08:44.601837',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Viktoriya Volgina'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Hello everyone, let me introduce myself:\n'
                '\n'
                'I am Quantum StarMan, the electrifying, freshest member of '
                'the Quantum EAs family. \n'
                '\n'
                "I'm a fully automated, multicurrency EA with the power to "
                'handle up to 5 dynamic pairs: AUDUSD, EURAUD, EURUSD, GBPUSD, '
                'and USDCAD . With the utmost precision and unwavering '
                "responsibility, I'll take your trading game to the next "
                "level. Here's the kicker: I don't rely on Martingale "
                'strategies. Instead, I utilize a sophisticated grid system '
                "that's designed for peak perfor",
 'images': ['https://c.mql5.com/31/1404/quantum-starman-logo-200x200-9143.png'],
 'market_id': 'mt5',
 'name': 'Quantum StarMan',
 'price': 499.99,
 'product_id': '107189',
 'rating': 4.92,
 'reviews_count': 91,
 'scraped_at': '2025-06-13T15:08:44.604418',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AI ZeroPoint Dynamics EA Cognitive Signal Architecture | '
                'Multi-Asset Precision Engine “Not an EA. Not a strategy. A '
                'living system of inference, adaptation, and execution.” BORN '
                'FROM THE ZERO POINT AI ZeroPoint Dynamics EA is not built — '
                'it is calibrated.\n'
                'Not coded — but architected to function as a real-time '
                'cognitive organism , responding to markets with a depth of '
                'reasoning that mirrors human decision-making — yet surpasses '
                'it in scale, consistency, and velocity. At the heart of '
                'ZeroPoint lie',
 'images': ['https://c.mql5.com/31/1383/ai-zeropoint-dynamics-mt5-logo-200x200-4072.png'],
 'market_id': 'mt5',
 'name': 'AI ZeroPoint Dynamics MT5',
 'price': 499.0,
 'product_id': '138199',
 'rating': 5.0,
 'reviews_count': 7,
 'scraped_at': '2025-06-13T15:08:44.607308',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Peter Robert Grange'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 140377: name='BreakTrue AI MT5', price=499.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 114522: name='Bitcoin Robot MT5', price=1599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 35049: name='TradePanel MT5', price=100.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The XG Gold Robot MT5 is specially designed for Gold. We '
                'decided to include this EA in our offering after extensive '
                'testing .\xa0XG Gold Robot and works perfectly with the '
                'XAUUSD, GOLD, XAUEUR pairs. XG Gold Robot has been created '
                'for all traders who like to Trade in Gold and includes '
                'additional a function that displays weekly Gold levels with '
                'the minimum and maximum displayed in the panel as well as on '
                'the chart, which will help you in manual trading. It’s a '
                'strategy based on Price Action, Cycle S',
 'images': ['https://c.mql5.com/31/1058/xg-gold-robot-mt5-logo-200x200-7278.png'],
 'market_id': 'mt5',
 'name': 'XG Gold Robot MT5',
 'price': 899.0,
 'product_id': '92195',
 'rating': 4.27,
 'reviews_count': 83,
 'scraped_at': '2025-06-13T15:08:44.612545',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Welcome to Trade Manager MT5 - the ultimate risk management '
                'tool designed to make trading more intuitive, precise, and '
                "efficient. This is not just an order placement tool; it's a "
                'comprehensive solution for seamless trade planning, position '
                "management, and enhanced control over risk. Whether you're a "
                'beginner taking your first steps, an advanced trader, or a '
                'scalper needing rapid executions, Trade Manager MT5 adapts to '
                'your needs, offering flexibility across all markets, from '
                'forex and indices t',
 'images': ['https://c.mql5.com/31/405/forex-trade-manager-mt5-logo-200x200-4875.png'],
 'market_id': 'mt5',
 'name': 'Forex Trade Manager MT5',
 'price': 99.0,
 'product_id': '39150',
 'rating': 4.98,
 'reviews_count': 522,
 'scraped_at': '2025-06-13T15:08:44.615714',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'InvestSoft'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Eternal Engine is an advanced EA that integrates multiple '
                'indicators with grid and Martingale strategies. Its core '
                'feature is precise entry point control, enabling it to '
                'perform exceptionally well even in complex market '
                'environments. Eternal Engine EA offers numerous trading '
                'opportunities, is not sensitive to spreads, and ensures '
                'accurate execution of every trade through strict entry point '
                'management. The strategy has been proven in live trading, '
                'providing over a year of low-drawdown real-time s',
 'images': ['https://c.mql5.com/31/1008/eternal-engine-ea-mt5-logo-200x200-9599.png'],
 'market_id': 'mt5',
 'name': 'Eternal Engine EA MT5',
 'price': 599.0,
 'product_id': '109036',
 'rating': 5.0,
 'reviews_count': 17,
 'scraped_at': '2025-06-13T15:08:44.618960',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Wei Tu'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'PUMPING STATION – Your Personal All-inclusive strategy\n'
                'Introducing\xa0PUMPING STATION\xa0— a revolutionary Forex '
                'indicator that will transform your trading into an exciting '
                'and effective activity! This indicator is not just an '
                'assistant but a full-fledged trading system with powerful '
                'algorithms that will help you start trading more stable! When '
                'you purchase this product, you also get FOR FREE: Exclusive '
                'Set Files:\xa0For automatic setup and maximum performance. '
                'Step-by-step video manual:\xa0Learn how to tr',
 'images': ['https://c.mql5.com/31/1323/algo-pumping-logo-200x200-1904.png'],
 'market_id': 'mt5',
 'name': 'Algo Pumping',
 'price': 99.0,
 'product_id': '134062',
 'rating': 5.0,
 'reviews_count': 15,
 'scraped_at': '2025-06-13T15:08:44.621660',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 136145: name='Venom Us30 Scalp', price=266.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 122975: name='DS Gold Robot MT5', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 129661: name='EvoTrade EA MT5', price=799.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 117023: name='Supply Demand EA ProBot MT5', price=589.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'BreakTrue AI: Major levels breakout / rebound trading system '
                'with AI assistance. AI is optional and is used in the way it '
                'is supposed to be. \n'
                'Finally, the Expert Advisor which uses AI in the right way! '
                'BreakTrue AI combines sophisticated built-in trading strategy '
                'based on major levels true and false breakouts, with the '
                'cutting-edge technology of OpenAI’s ChatGPT which servers as '
                'additional entry filter. This isn’t just another empty claim '
                '— BreakTrue AI provides a genuine, fully integrated AI so',
 'images': ['https://c.mql5.com/31/1408/breaktrue-ai-mt5-logo-200x200-7785.png'],
 'market_id': 'mt5',
 'name': 'BreakTrue AI MT5',
 'price': 499.0,
 'product_id': '140377',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:08:44.627342',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Andrey Barinov'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The Bitcoin Robot MT5 is engineered to execute Bitcoin trades '
                'with unparalleled efficiency and precision . Developed by a '
                'team of experienced traders and developers, our Bitcoin Robot '
                'employs a sophisticated algorithmic approach (price action, '
                'trend as well as two personalized indicators) to analyze '
                'market and execute trades swiftly with M5 timeframe , '
                'ensuring that you never miss out on lucrative opportunities. '
                'No grid, no martingale, no hedging, EA only open one position '
                'at the same time. Bit',
 'images': ['https://c.mql5.com/31/1079/bitcoin-robot-mt5-logo-200x200-2796.png'],
 'market_id': 'mt5',
 'name': 'Bitcoin Robot MT5',
 'price': 1599.0,
 'product_id': '114522',
 'rating': 4.57,
 'reviews_count': 109,
 'scraped_at': '2025-06-13T15:08:44.632228',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Trade Panel is a multifunctional trading assistant. The '
                'application contains more than 50 trading functions for '
                'manual trading and allows you to automate most trading '
                'operations. Instructions for installing the application | '
                'Instructions for the application | Trial version of the '
                'application for a demo account Trade. Allows you to perform '
                'trading operations in one click: Open pending orders and '
                'positions with automatic risk calculation. Open multiple '
                'orders and positions with one click. Open ord',
 'images': ['https://c.mql5.com/31/1369/tradepanel-mt5-logo-200x200-2148.png'],
 'market_id': 'mt5',
 'name': 'TradePanel MT5',
 'price': 100.0,
 'product_id': '35049',
 'rating': 4.84,
 'reviews_count': 133,
 'scraped_at': '2025-06-13T15:08:44.635117',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Alfiya Fazylova'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 111357: name='The Gold Reaper MT5', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 128606: name='SmartChoise', price=349.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 140434: name='ARIA Connector EA', price=300.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 128417: name='Prometheus MT5', price=750.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Venom US30 Scalp — Pure Precision for US30 Trading Venom US30 '
                'Scalp is a fully automated Expert Advisor built for US30 (Dow '
                'Jones Index) on the M30 timeframe . It runs on a proprietary '
                'mathematical engine — no indicators, no martingale, no grid — '
                'just clean, logic-based trading. SIGNAL : Ask for signal Core '
                'Features Trend-following strategy with multi-layer '
                'confirmations Default risk: 0.01 lot per $500 (adjustable) '
                'Internal controls to reduce risk exposure Simple '
                'plug-and-play setup, no optimiz',
 'images': ['https://c.mql5.com/31/1366/venom-us30-scalp-logo-200x200-5259.png'],
 'market_id': 'mt5',
 'name': 'Venom Us30 Scalp',
 'price': 266.0,
 'product_id': '136145',
 'rating': 5.0,
 'reviews_count': 9,
 'scraped_at': '2025-06-13T15:08:44.640095',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antoine Melhem'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing the DS Gold Robot, your ultimate companion in '
                'navigating the intricate world of XAUUSD trading. Developed '
                'with precision and powered by cutting-edge algorithms, DS '
                'Gold is a forex robot meticulously crafted to optimize your '
                'trading performance with\xa0 XAUUSD pairs . With its advanced '
                'analytical capabilities,\xa0 DS Gold \xa0Robot \xa0 '
                'constantly monitors the gold market, identifying key trends , '
                'patterns, and price movements with lightning speed. The DS '
                'Gold Robot opens positions every day from',
 'images': ['https://c.mql5.com/31/1182/ds-gold-robot-mt5-logo-200x200-9360.png'],
 'market_id': 'mt5',
 'name': 'DS Gold Robot MT5',
 'price': 599.0,
 'product_id': '122975',
 'rating': 4.25,
 'reviews_count': 24,
 'scraped_at': '2025-06-13T15:08:44.642858',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'EvoTrade: The First Self-Learning Trading System on the '
                'Market Allow me to introduce EvoTrade —a unique trading '
                'advisor built using cutting-edge technologies in computer '
                'vision and data analysis. It is the first self-learning '
                'trading system on the market, operating in real-time. '
                'EvoTrade analyzes market conditions, adapts strategies, and '
                'dynamically adjusts to changes, delivering exceptional '
                'precision in any environment. EvoTrade employs advanced '
                'neural networks, including Long Short-Term Memory',
 'images': ['https://c.mql5.com/31/1267/evotrade-ea-mt5-logo-200x200-5183.png'],
 'market_id': 'mt5',
 'name': 'EvoTrade EA MT5',
 'price': 799.0,
 'product_id': '129661',
 'rating': 4.67,
 'reviews_count': 18,
 'scraped_at': '2025-06-13T15:08:44.645210',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dolores Martin Munoz'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Fully Automated EA based on Supply and Demand Principles. The '
                'first Supply and Demand EA that is offering Complete '
                'Automation . Now trading Becomes Effortless, offering full '
                'control over your trading strategy through a User-Friendly '
                'graphical Trading Panel. You get a Super High Quality '
                'Algorithmic Trading Software that covers all trading styles '
                'Manual, Semi-Auto and Full-Auto.\xa0Through various settings '
                'and customization options, every trader can create a strategy '
                'that fits their own needs and per',
 'images': ['https://c.mql5.com/31/1110/supply-demand-ea-probot-mt5-logo-200x200-5181.png'],
 'market_id': 'mt5',
 'name': 'Supply Demand EA ProBot MT5',
 'price': 589.0,
 'product_id': '117023',
 'rating': 4.62,
 'reviews_count': 13,
 'scraped_at': '2025-06-13T15:08:44.647696',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Georgios Kalomoiropoulos'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 136885: name='DeepAlgo GPX ML', price=198.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 104565: name='Scalper Deriv', price=380.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 122619: name='Mean Machine', price=1797.53
2025-06-13 15:08:44 [market] INFO: Extracted item for product 140128: name='GoldMasterFusion MT5', price=699.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 68951: name='Local Trade Copier EA MT5', price=50.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'PROP FIRM READY! ( download SETFILE ) LAUNCH PROMO: Only a '
                'few copies left at current price! Final price: 990$ Get 1 EA '
                'for free (for 2 trade accounts) -> contact me after purchase '
                'Ultimate Combo Deal \xa0 -> \xa0 click here JOIN PUBLIC '
                'GROUP: \xa0 Click here \n'
                'Live Signal\n'
                '\n'
                'Welcome to the Gold Reaper! Build on the very succesfull '
                'Goldtrade Pro, this EA has been designed to run on multiple '
                'timeframes at the same time, and has the option to set the '
                'trade frequency from very conservative to extreme volatile',
 'images': ['https://c.mql5.com/31/1365/the-gold-reaper-mt5-logo-200x200-7714.png'],
 'market_id': 'mt5',
 'name': 'The Gold Reaper MT5',
 'price': 599.0,
 'product_id': '111357',
 'rating': 4.34,
 'reviews_count': 82,
 'scraped_at': '2025-06-13T15:08:44.651834',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Profalgo Limited'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'SmartChoise EA – Neural Network–Powered Trading System for '
                'XAU/USD (Gold) on M1 Timeframe\xa0\n'
                '\n'
                'The user manual is available via the link in my profile '
                'page.\n'
                'This EA is built for long-term, controlled '
                'growth—understanding and aligning it with your risk tolerance '
                'is key to its success. Uses a neural network–based engine '
                'that continuously analyzes real-time market data to adapt '
                'trading strategies according to current market conditions. '
                'This approach helps optimize trade entries, improve risk '
                'control,',
 'images': ['https://c.mql5.com/31/1305/smartchoise-logo-200x200-7053.png'],
 'market_id': 'mt5',
 'name': 'SmartChoise',
 'price': 349.0,
 'product_id': '128606',
 'rating': 4.05,
 'reviews_count': 37,
 'scraped_at': '2025-06-13T15:08:44.654875',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Gabriel Costin Floricel'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aria Connector EA (MT5 to ChatGPT and more...) Public '
                'channel:\xa0 https://www.mql5.com/en/channels/binaryforexea \n'
                'Many EAs on the market claim to use artificial intelligence '
                'or "neural networks" when in reality they only run '
                'traditional logic or connect with unreliable sources. Aria '
                'Connector EA was created with a clear and transparent '
                'purpose: to directly connect your MT5 platform with OpenAI’s '
                'AI , with no middlemen or shady scripts. From its first '
                'version, Aria establishes a real connection wit',
 'images': ['https://c.mql5.com/31/1412/aria-connector-ea-logo-200x200-8566.png'],
 'market_id': 'mt5',
 'name': 'ARIA Connector EA',
 'price': 300.0,
 'product_id': '140434',
 'rating': 5.0,
 'reviews_count': 1,
 'scraped_at': '2025-06-13T15:08:44.657558',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Martin Alejandro Bamonte'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Big sale 50% OFF! Price $750. Regular price $1499 All our '
                'signals are now available on myfxbook: \xa0 click here \xa0 '
                'Unique set files and all recommendations are provided free of '
                'charge. All future updates of the adviser are included in the '
                'price. After the purchase, contact me and I will help you '
                'install and configure the robot correctly. I will also share '
                'with you information on how to get a free VPS from a reliable '
                'broker. Gold is one of the riskiest instruments on the '
                'market. It requires precisi',
 'images': ['https://c.mql5.com/31/1394/prometheus-mt5-logo-200x200-1628.png'],
 'market_id': 'mt5',
 'name': 'Prometheus MT5',
 'price': 750.0,
 'product_id': '128417',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:08:44.660433',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Evgenii Aksenov'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 138632: name='TrendX Gold Scalper', price=493.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 117386: name='HFT PropFirm EA MT5', price=200.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 139996: name='AI No Stress FX MT5', price=299.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 134947: name='Pure AI', price=199.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'DeepAlgo GPX ML - Real-time Analysis by Intelligent AI '
                'Models \n'
                'When you buy an AI EA, you have no idea if the EA is actually '
                'using AI. With DeepAlgo GPX ML, there are actual API calls '
                'directly to DeepSeek for every single trade and you can see '
                'this reflected on your API dashboard in real time. The API '
                'keys are 100% owned by you and you can manage and use them '
                'however you like. There are multiple fail-safes in place in '
                'order to manage unexpected problems with the API in case the '
                'API server is eve',
 'images': ['https://c.mql5.com/31/1378/deepalgo-gpx-ml-logo-200x200-2368.png'],
 'market_id': 'mt5',
 'name': 'DeepAlgo GPX ML',
 'price': 198.0,
 'product_id': '136885',
 'rating': 5.0,
 'reviews_count': 1,
 'scraped_at': '2025-06-13T15:08:44.665587',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Connor Michael Woodson'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing Scalper Deriv: Elevating Your Scalping '
                'Experience. Important : Scalper Deriv is ONLY sold through '
                'mql5.com. Any other website is a scam and is not Scalper '
                'Deriv.\xa0 To download the updated configuration files and '
                'additional strategies, click here For the user guide, click '
                'here . Are you one of those traders who find their passion in '
                'scalping and want to make the most of your capital? Whether '
                'you have a balance of $20, $200, $2000, $20000, or even '
                '$200000 in your account, we have the p',
 'images': ['https://c.mql5.com/31/1230/scalper-deriv-logo-200x200-9680.png'],
 'market_id': 'mt5',
 'name': 'Scalper Deriv',
 'price': 380.0,
 'product_id': '104565',
 'rating': 4.88,
 'reviews_count': 17,
 'scraped_at': '2025-06-13T15:08:44.668070',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antonio Simon Del Vecchio'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing Version 8.2—A Revolutionary Leap in AI Trading '
                "Technology I'm proud to announce my most significant update "
                'yet: Version 8.2. This groundbreaking release introduces AI '
                'Position Management and AI Web Search, which dynamically '
                'modifies Take Profit and Stop Loss levels in real-time while '
                'searching the internet for breaking news, political events, '
                'interest rates, and market sentiment, ensuring optimal '
                'position management with priority handling across all '
                'symbols. Version 8.2 harnesses th',
 'images': ['https://c.mql5.com/31/1401/mean-machine-logo-200x200-6801.png'],
 'market_id': 'mt5',
 'name': 'Mean Machine',
 'price': 1797.53,
 'product_id': '122619',
 'rating': 5.0,
 'reviews_count': 33,
 'scraped_at': '2025-06-13T15:08:44.670149',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Fusion of Tradition and Innovation—Ushering in a New Era of '
                'Intelligent Trading 【Product Essence】 This EA is specifically '
                'designed for international gold (XAUUSD), ingeniously '
                'combining classic quantitative trading models with modern '
                'intelligent analytical technologies. It deeply explores '
                'market structure and volatility characteristics to achieve '
                'robust and efficient capital growth. The strategy '
                'demonstrates a steadily rising equity curve and low drawdown '
                'in various market environments, making t',
 'images': ['https://c.mql5.com/31/1406/goldmasterfusion-mt5-logo-200x200-6749.png'],
 'market_id': 'mt5',
 'name': 'GoldMasterFusion MT5',
 'price': 699.0,
 'product_id': '140128',
 'rating': 5.0,
 'reviews_count': 3,
 'scraped_at': '2025-06-13T15:08:44.672740',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Chen Jia Qi'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Experience exceptionally fast trade copying with the \xa0 '
                'Local Trade Copier EA MT5 . With its easy 1-minute setup, '
                'this trade copier allows you to copy trades between multiple '
                'MetaTrader terminals on the same Windows computer or Windows '
                'VPS with lightning-fast copying speeds of under 0.5 seconds. '
                "Whether you're a beginner or a professional trader, the \xa0 "
                'Local Trade Copier EA MT5 \xa0 offers a wide range of options '
                "to customize it to your specific needs. It's the ultimate "
                'solution for anyone looking t',
 'images': ['https://c.mql5.com/31/946/local-trade-copier-ea-mt5-logo-200x200-6081.png'],
 'market_id': 'mt5',
 'name': 'Local Trade Copier EA MT5',
 'price': 50.0,
 'product_id': '68951',
 'rating': 4.98,
 'reviews_count': 83,
 'scraped_at': '2025-06-13T15:08:44.674988',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Juvenille Emperor Limited'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 135294: name='PrizmaL Scalper', price=399.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 47785: name='Trend Screener Pro MT5', price=50.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 118127: name='Exp5 AI Sniper for MT5', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 136587: name='Spiral Ascend', price=349.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing TrendX Gold Scalper – The Next Evolution in Gold '
                'Trading Automation LAUNCH PROMO:\xa0 Only 2 copies left at '
                'current price. Next price is $553 The price will increase by '
                '$100 with every 10 purchases\xa0 Final price: $1933 \n'
                'LIVE SIGNAL ($100K account) :\xa0 '
                'https://www.mql5.com/en/signals/2304986?source=Site+Profile+Seller '
                'LIVE SIGNAL SMALL:\xa0 '
                'https://www.mql5.com/en/signals/2307472?source=Site+Profile+Seller '
                'LIVE COMBO TrendX + US30 Scalper + EURUSD Algo:\xa0 '
                'https://www.mql5.com/en/signals/23019',
 'images': ['https://c.mql5.com/31/1384/trendx-gold-scalper-logo-200x200-7715.png'],
 'market_id': 'mt5',
 'name': 'TrendX Gold Scalper',
 'price': 493.0,
 'product_id': '138632',
 'rating': 5.0,
 'reviews_count': 1,
 'scraped_at': '2025-06-13T15:08:44.679935',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Lo Thi Mai Loan'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'HFT PropFirm EA MT5 is\xa0 also known as Green Man due to its '
                'distinctive logo by Dilwyn Tng, is an Expert Advisor (EA) '
                'crafted specifically for overcoming challenges or evaluations '
                'from proprietary trading firms (prop firms) that permit '
                'High-Frequency Trading (HFT) strategies.\n'
                '\n'
                'Now Greenman\xa0 HFT PropFirm EA MT5 is fully automatic! '
                'Free\xa0 1 All-In-One Breakout EA account licence with '
                'purchase of HFT PropFirm EA MT5 \n'
                'Passing HFT MT5 Challenge Performance Monitor: \n'
                'Broker: Fusion Market\n'
                'Login:\xa0\xa0172147',
 'images': ['https://c.mql5.com/31/1114/hft-propfirm-ea-mt5-logo-200x200-8392.png'],
 'market_id': 'mt5',
 'name': 'HFT PropFirm EA MT5',
 'price': 200.0,
 'product_id': '117386',
 'rating': 4.92,
 'reviews_count': 48,
 'scraped_at': '2025-06-13T15:08:44.682447',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dilwyn Tng'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'No Stress FX — When Trading Feels a Bit Calmer When I started '
                'building No Stress FX, I wasn’t trying to chase miracles.\n'
                'The goal was to create something that fits a slower, more '
                'mindful style —\n'
                'without pressure, without noise, and hopefully with more '
                'clarity. It’s the kind of trading approach that simply makes '
                'more sense to me. Just being transparent Live Signal:\xa0 '
                'https://www.mql5.com/en/signals/2313926 \n'
                'Yes, it’s running on a real account.\n'
                'Some traders have found it interesting, and activity is',
 'images': ['https://c.mql5.com/31/1407/ai-no-stress-fx-mt5-logo-200x200-9829.png'],
 'market_id': 'mt5',
 'name': 'AI No Stress FX MT5',
 'price': 299.0,
 'product_id': '139996',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:08:44.685697',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Mariia Aborkina'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '3 copies left at $199\n'
                'Next price $299 Unique trading advisor for EURUSD\n'
                'The advisor is a modular trading system. It is based on an '
                'architecture in which each trading decision is formed not by '
                'a monolithic algorithm, but as a result of the interaction of '
                'independent logical blocks - indicator filters, entry '
                'conditions, exits and control rules. IMPORTANT! After '
                'purchase, send me a private message to receive the '
                'installation guide and setup instructions. Live signal '
                'Strategy XAUUSD - https://www.m',
 'images': ['https://c.mql5.com/31/1335/pure-ai-logo-200x200-4104.png'],
 'market_id': 'mt5',
 'name': 'Pure AI',
 'price': 199.0,
 'product_id': '134947',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:08:44.689481',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vitali Vasilenka'}
2025-06-13 15:08:44 [market] INFO: Extracted item for product 138518: name='EasyInsight AIO MT5', price=249.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 134248: name='Dax Killer', price=599.0
2025-06-13 15:08:44 [market] INFO: Extracted item for product 133718: name='Zen Flow 2', price=399.0
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Live signal \n'
                '\n'
                'Find out more here: \xa0 '
                'https://www.mql5.com/en/users/prizmal/seller \n'
                'PrizmaL Scalper - Intraday Scalping for XAUUSD \n'
                'This trading algorithm is designed for speculative trading in '
                'the spot gold market XAUUSD.\n'
                'It employs advanced market microstructure analysis '
                'techniques, reacting to price impulses and liquidity in real '
                'time. The algorithm is not subject to swaps, making it '
                'particularly effective for active intraday trading.\n'
                'Optimized risk management and dynamic adaptation to volatil',
 'images': ['https://c.mql5.com/31/1388/prizmal-scalper-logo-200x200-7045.png'],
 'market_id': 'mt5',
 'name': 'PrizmaL Scalper',
 'price': 399.0,
 'product_id': '135294',
 'rating': 3.14,
 'reviews_count': 22,
 'scraped_at': '2025-06-13T15:08:44.695281',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladimir Lekhovitser'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'Unlock the Power of Trends Trading with the Trend Screener '
                'Indicator: Your Ultimate Trend Trading Solution powered by '
                'Fuzzy Logic and Multi-Currencies System! Elevate your trading '
                'game with the Trend Screener, the revolutionary trend '
                'indicator designed to transform your Metatrader into a '
                'powerful Trend Analyzer. This comprehensive tool leverages '
                'fuzzy logic and integrates over 13 premium features and three '
                'trading strategies, offering unmatched precision and '
                'versatility. LIMITED TIME OFFER : Tre',
 'images': ['https://c.mql5.com/31/1414/trend-screener-pro-mt5-logo-200x200-7413.png'],
 'market_id': 'mt5',
 'name': 'Trend Screener Pro MT5',
 'price': 50.0,
 'product_id': '47785',
 'rating': 4.86,
 'reviews_count': 84,
 'scraped_at': '2025-06-13T15:08:44.698086',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'STE S.S.COMPANY'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Our team is thrilled to introduce Trading Robot, the '
                'cutting-edge Smart Trading Expert Advisor for the MetaTrader '
                'terminal. AI Sniper is an intelligent, self-optimizing '
                'trading robot designed for MT5 . It leverages a smart '
                'algorithm and advanced trading strategies to maximize your '
                'trading potential. With 15 years of experience in trading '
                'exchanges and the stock market, we have developed innovative '
                'strategy management features, additional intelligent '
                'functions, and a user-friendly graphical inte',
 'images': ['https://c.mql5.com/31/1268/exp5-ai-sniper-for-mt5-logo-200x200-7487.png'],
 'market_id': 'mt5',
 'name': 'Exp5 AI Sniper for MT5',
 'price': 599.0,
 'product_id': '118127',
 'rating': 4.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:08:44.700426',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladislav Andruschenko'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Spiral Ascend EA – Smart Automated Trading for XAUUSD \n'
                'The\xa0 Spiral Ascend \xa0 EA is a powerful, fully automated '
                'trading solution tailored for the XAUUSD (Gold) market , '
                'combining classical Fibonacci methodologies with modern '
                'technical analysis and advanced volume-based logic. Developed '
                'with flexibility and precision in mind, this EA is ideal for '
                'traders seeking a disciplined and logic-driven approach to '
                'algorithmic trading—including those working under the '
                'conditions of prop trading firms . Buy S',
 'images': ['https://c.mql5.com/31/1368/spiral-ascend-logo-200x200-4715.png'],
 'market_id': 'mt5',
 'name': 'Spiral Ascend',
 'price': 349.0,
 'product_id': '136587',
 'rating': 4.93,
 'reviews_count': 14,
 'scraped_at': '2025-06-13T15:08:44.703096',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'DRT Circle'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'EASY Insight AIO – All-in-One Market Scanner with Full '
                'Indicator Data What if you could analyze the entire Forex '
                'market in seconds – without plotting anything on your '
                'charts? \n'
                'EASY Insight AIO integrates the full analytical output from '
                'FX Power, FX Volume, FX Dynamic, and FX Levels – into a '
                'single, structured CSV file. No extra tools needed. No chart '
                'overlays. No distractions. \n'
                '⸻ \n'
                'Why Use EASY Insight AIO? All Indicators Included: No need '
                'for additional licenses – AIO comes with full access to',
 'images': ['https://c.mql5.com/31/1384/easyinsight-aio-mt5-logo-200x200-8116.png'],
 'market_id': 'mt5',
 'name': 'EasyInsight AIO MT5',
 'price': 249.0,
 'product_id': '138518',
 'rating': 5.0,
 'reviews_count': 4,
 'scraped_at': '2025-06-13T15:08:44.707304',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Alain Verleyen'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'After 6 Years of Successful Manual Trading, My Strategies Are '
                'Now Available as Expert Advisors! \n'
                'Introducing the DAX Killer EA – a trading system built for '
                'the DAX Index from years of hands-on experience, extensive '
                'testing, and a steadfast commitment to secure, strategic '
                'trading. NO GRID, NO MARTINGALE, TIGHT SL EVERY TRADE. ONE '
                'TRADE PER DAY . \xa0 NO LOT MULTIPLIER. \xa0The price of the '
                'EA will increase by $100 with every 10 purchases. ICTRADING '
                'LIVE SIGNAL \xa0 DAX Killer Public \xa0 Chat \xa0 Group \xa0 '
                'IMPOR',
 'images': ['https://c.mql5.com/31/1337/dax-killer-logo-200x200-2452.png'],
 'market_id': 'mt5',
 'name': 'Dax Killer',
 'price': 599.0,
 'product_id': '134248',
 'rating': 3.67,
 'reviews_count': 3,
 'scraped_at': '2025-06-13T15:08:44.711049',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Pablo Dominguez Sanchez'}
2025-06-13 15:08:44 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'LAUNCH PROMO: Final price: 1,700$ Only 2 copies left at $399. '
                'Next price will be $499 Get 1 EA for free (for 2 trade '
                'accounts) -> contact me after purchase Instruction Blog Link '
                'to Channel \n'
                'Welcome to ZenFlow! ZenFlow is an advanced EA designed to '
                'adapt to changing market trends with precision and speed. It '
                'is optimized to trade the XAUUSD( or GOLD) symbol and should '
                'be run on only one chart. This EA uses a sophisticated '
                'trend-following strategy combined with a momentum-based '
                'indicator that ide',
 'images': ['https://c.mql5.com/31/1318/zen-flow-2-logo-200x200-9786.png'],
 'market_id': 'mt5',
 'name': 'Zen Flow 2',
 'price': 399.0,
 'product_id': '133718',
 'rating': 2.72,
 'reviews_count': 29,
 'scraped_at': '2025-06-13T15:08:44.715510',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Hamza Ashraf'}
2025-06-13 15:08:44 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:08:44 [market] INFO: Exported 1 items to output/market_20250613_150844.json
2025-06-13 15:08:44 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 296,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.430375,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 8, 44, 727842, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'item_dropped_count': 69,
 'item_dropped_reasons_count/DropItem': 69,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 81,
 'log_count/WARNING': 73,
 'memusage/max': 75100160,
 'memusage/startup': 75100160,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 8, 44, 297467, tzinfo=datetime.timezone.utc)}
2025-06-13 15:08:44 [scrapy.core.engine] INFO: Spider closed (finished)
2025-06-13 15:09:25 [scrapy.utils.log] INFO: Scrapy 2.13.2 started (bot: voyagr_scrapy)
2025-06-13 15:09:25 [scrapy.utils.log] INFO: Versions:
{'lxml': '5.4.0',
 'libxml2': '2.13.8',
 'cssselect': '1.3.0',
 'parsel': '1.10.0',
 'w3lib': '2.3.1',
 'Twisted': '25.5.0',
 'Python': '3.11.3 (main, Nov  8 2024, 10:58:25) [GCC 11.4.0]',
 'pyOpenSSL': '25.1.0 (OpenSSL 3.5.0 8 Apr 2025)',
 'cryptography': '45.0.4',
 'Platform': 'Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.35'}
2025-06-13 15:09:25 [scrapy.addons] INFO: Enabled addons:
[]
2025-06-13 15:09:25 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/utils/request.py:120: ScrapyDeprecationWarning: 'REQUEST_FINGERPRINTER_IMPLEMENTATION' is a deprecated setting.
It will be removed in a future version of Scrapy.
  return cls(crawler)

2025-06-13 15:09:26 [scrapy.middleware] INFO: Enabled extensions:
['scrapy.extensions.corestats.CoreStats',
 'scrapy.extensions.memusage.MemoryUsage',
 'scrapy.extensions.logstats.LogStats',
 'scrapy.extensions.throttle.AutoThrottle']
2025-06-13 15:09:26 [scrapy.crawler] INFO: Overridden settings:
{'AUTOTHROTTLE_ENABLED': True,
 'AUTOTHROTTLE_START_DELAY': 1,
 'BOT_NAME': 'voyagr_scrapy',
 'DOWNLOAD_DELAY': 1,
 'DOWNLOAD_TIMEOUT': 30,
 'EDITOR': 'code',
 'HTTPCACHE_ENABLED': True,
 'HTTPCACHE_EXPIRATION_SECS': 3600,
 'LOG_FILE': 'scrapy.log',
 'LOG_LEVEL': 'INFO',
 'NEWSPIDER_MODULE': 'voyagr_scrapy.spiders',
 'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
 'REQUEST_FINGERPRINTER_IMPLEMENTATION': '2.7',
 'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
 'RETRY_TIMES': 3,
 'SPIDER_MODULES': ['voyagr_scrapy.spiders'],
 'TELNETCONSOLE_ENABLED': False,
 'USER_AGENT': 'voyagr_scrapy (+http://www.yourdomain.com)'}
2025-06-13 15:09:26 [scrapy.middleware] INFO: Enabled downloader middlewares:
['scrapy.downloadermiddlewares.offsite.OffsiteMiddleware',
 'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware',
 'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware',
 'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware',
 'voyagr_scrapy.middlewares.RotateUserAgentMiddleware',
 'scrapy.downloadermiddlewares.retry.RetryMiddleware',
 'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware',
 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware',
 'scrapy.downloadermiddlewares.redirect.RedirectMiddleware',
 'scrapy.downloadermiddlewares.cookies.CookiesMiddleware',
 'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware',
 'scrapy.downloadermiddlewares.stats.DownloaderStats',
 'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware']
2025-06-13 15:09:26 [scrapy.middleware] INFO: Enabled spider middlewares:
['scrapy.spidermiddlewares.start.StartSpiderMiddleware',
 'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware',
 'voyagr_scrapy.middlewares.SpiderMiddleware',
 'scrapy.spidermiddlewares.referer.RefererMiddleware',
 'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware',
 'scrapy.spidermiddlewares.depth.DepthMiddleware']
2025-06-13 15:09:26 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/core/spidermw.py:98: ScrapyDeprecationWarning: The following enabled spider middlewares, directly or through their parent classes, define the deprecated process_start_requests() method: voyagr_scrapy.middlewares.SpiderMiddleware. process_start_requests() has been deprecated in favor of a new method, process_start(), to support asynchronous code execution. process_start_requests() will stop being called in a future version of Scrapy. If you use Scrapy 2.13 or higher only, replace process_start_requests() with process_start(); note that process_start() is a coroutine (async def). If you need to maintain compatibility with lower Scrapy versions, when defining process_start_requests() in a spider middleware class, define process_start() as well. See the release notes of Scrapy 2.13 for details: https://docs.scrapy.org/en/2.13/news.html
  warn(

2025-06-13 15:09:26 [scrapy.core.spidermw] WARNING: Middleware voyagr_scrapy.middlewares.SpiderMiddleware doesn't support asynchronous spider output, this is deprecated and will stop working in a future version of Scrapy. The middleware should be updated to support it. Please see https://docs.scrapy.org/en/latest/topics/coroutines.html#for-middleware-users for more information.
2025-06-13 15:09:26 [scrapy.middleware] INFO: Enabled item pipelines:
['voyagr_scrapy.pipelines.json_pipeline.ValidationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.DeduplicationPipeline',
 'voyagr_scrapy.pipelines.json_pipeline.JsonExportPipeline']
2025-06-13 15:09:26 [scrapy.core.engine] INFO: Spider opened
2025-06-13 15:09:26 [scrapy.extensions.logstats] INFO: Crawled 0 pages (at 0 pages/min), scraped 0 items (at 0 items/min)
2025-06-13 15:09:26 [market] INFO: Spider opened: market
2025-06-13 15:09:26 [py.warnings] WARNING: /home/<USER>/perso/voyagr_scrapy/.venv/lib/python3.11/site-packages/scrapy/spidermiddlewares/base.py:46: ScrapyDeprecationWarning: The Spider.start_requests() method is deprecated, use Spider.start() instead. If you are calling super().start_requests() from a Spider.start() override, iterate super().start() instead.
  for o in start:

2025-06-13 15:09:26 [market] INFO: Found 140 product links on https://www.mql5.com/en/market/mt5
2025-06-13 15:09:26 [market] INFO: Extracted item for product 118805: name='Quantum Queen MT5', price=1199.99
2025-06-13 15:09:26 [market] INFO: Extracted item for product 137361: name='Swing Master EA', price=999.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 103452: name='Quantum Emperor MT5', price=1149.99
2025-06-13 15:09:26 [market] INFO: Extracted item for product 127013: name='Quantum Bitcoin EA', price=899.99
2025-06-13 15:09:26 [market] INFO: Extracted item for product 137760: name='GbpUsd Commander', price=499.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 130426: name='Beatrix Inventor MT5', price=1499.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 134329: name='AiQ', price=1837.97
2025-06-13 15:09:26 [market] INFO: Extracted item for product 125754: name='Monic', price=399.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 23415: name='Trade Assistant MT5', price=100.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 139787: name='FastWay EA', price=1287.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 133653: name='Ultimate Breakout System', price=999.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '1 copies left at $999 Next price $1099 Let me introduce you '
                'to an Expert Advisor, built on the foundation of my manual '
                'trading system — Algo Pumping .\xa0I seriously upgraded this '
                'strat, loaded it with key tweaks, filters, and tech hacks, '
                'and now I’m dropping a trading bot that: Crushes the markets '
                'with the advanced Algo Pumping Swing Trading algorithm, Slaps '
                'Stop Loss orders to protect your account, Perfectly fits both '
                '"Prop Firm Trading" and "Personal Trading", Trades clean '
                'without martingale or',
 'images': ['https://c.mql5.com/31/1367/swing-master-ea-logo-200x200-6523.png'],
 'market_id': 'mt5',
 'name': 'Swing Master EA',
 'price': 999.0,
 'product_id': '137361',
 'rating': 5.0,
 'reviews_count': 18,
 'scraped_at': '2025-06-13T15:09:26.254637',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing \xa0 Quantum Emperor EA , the groundbreaking MQL5 '
                "expert advisor that's transforming the way you trade the "
                'prestigious GBPUSD pair! Developed by a team of experienced '
                'traders with trading experience of over 13 years. IMPORTANT! '
                'After the purchase please send me a private message to '
                'receive the installation manual and the setup instructions. '
                '***Buy Quantum Emperor EA and you could get Quantum StarMan '
                'for free !*** Ask in private for more details Live Signal V5: '
                'Click Here \n'
                '\n'
                'MT4 Version :',
 'images': ['https://c.mql5.com/31/1404/quantum-emperor-mt5-logo-200x200-3721.png'],
 'market_id': 'mt5',
 'name': 'Quantum Emperor MT5',
 'price': 1149.99,
 'product_id': '103452',
 'rating': 4.86,
 'reviews_count': 414,
 'scraped_at': '2025-06-13T15:09:26.257123',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Quantum Bitcoin EA : There is no such thing as impossible, '
                "it's only a matter of figuring out how to do it! \n"
                'Step into the future of Bitcoin trading with Quantum Bitcoin '
                'EA , the latest masterpiece from one of the top MQL5 sellers. '
                'Designed for traders who demand performance, precision, and '
                "stability, Quantum Bitcoin redefines what's possible in the "
                'volatile world of cryptocurrency. \n'
                'IMPORTANT! After the purchase please send me a private '
                'message to receive the installation manual and the setup i',
 'images': ['https://c.mql5.com/31/1404/quantum-bitcoin-ea-logo-200x200-3009.png'],
 'market_id': 'mt5',
 'name': 'Quantum Bitcoin EA',
 'price': 899.99,
 'product_id': '127013',
 'rating': 5.0,
 'reviews_count': 73,
 'scraped_at': '2025-06-13T15:09:26.259296',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'GBPUSD Commander – Structured Scalping on M30-(lowest risk in '
                'the world) GBPUSD Commander is an Expert Advisor designed '
                'specifically for GBP/USD on the 30-minute timeframe. It '
                'applies structured technical logic with fixed Stop Loss and '
                'Take Profit levels, adaptive lot sizing, and risk control '
                'built into each position. The EA avoids the use of '
                'martingale, grid, or hedge techniques and operates with '
                'clearly defined trade logic for consistent execution. '
                'Suitable for both newer traders and experienc',
 'images': ['https://c.mql5.com/31/1415/gbpusd-commander-logo-200x200-1108.png'],
 'market_id': 'mt5',
 'name': 'GbpUsd Commander',
 'price': 499.0,
 'product_id': '137760',
 'rating': 5.0,
 'reviews_count': 10,
 'scraped_at': '2025-06-13T15:09:26.261542',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ibrahim Aljaref'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing my new Expert Advisor Beatrix Inventor, Beatrix '
                'Inventor EA uses the concept of following trends in '
                'conducting market analysis. Analyzing market trends with the '
                'main indicators Bollinger Band and Moving Average, when '
                'entering transactions, this EA also considers the Orderblock '
                'zone which makes the analysis more accurate. The algorithm '
                'used in developing this EA is a reliable algorithm both in '
                'entry and managing floating minus.\n'
                '\n'
                'This EA is designed to be used on the XAUUSD / GOLD pair',
 'images': ['https://c.mql5.com/31/1402/beatrix-inventor-mt5-logo-200x200-9075.png'],
 'market_id': 'mt5',
 'name': 'Beatrix Inventor MT5',
 'price': 1499.0,
 'product_id': '130426',
 'rating': 3.62,
 'reviews_count': 91,
 'scraped_at': '2025-06-13T15:09:26.263477',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Azil Al Azizul'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 135273: name='Burning Grid', price=999.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 134465: name='AI Neuro Dynamics MT5', price=599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 124990: name='King Sniper EA', price=599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 92199: name='Big Forex Players MT5', price=1999.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing AIQ Version 2.3—The Evolution of Autonomous '
                "Trading Intelligence I'm proud to present AIQ (Autonomous "
                'Intelligence), the next generation of AI-powered trading '
                'technology. Version 2.3 now features AI Web Search and '
                'Enhanced APM (AI Position Management) - AIQ can search the '
                'internet in real-time for breaking news, political events, '
                'interest rates, and market sentiment, while the upgraded APM '
                'manages your Take Profit and Stop Loss like a professional '
                'trader in real-time. Building on th',
 'images': ['https://c.mql5.com/31/1399/aiq-logo-200x200-1013.png'],
 'market_id': 'mt5',
 'name': 'AiQ',
 'price': 1837.97,
 'product_id': '134329',
 'rating': 4.83,
 'reviews_count': 29,
 'scraped_at': '2025-06-13T15:09:26.265898',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Live signal \n'
                '\n'
                'Find out more here: \xa0 '
                'https://www.mql5.com/en/users/prizmal/seller \n'
                'The strategy uses an averaging trading approach, relying on '
                'the Stochastic Oscillator and Bollinger Bands as the main '
                'indicators. It consistently implements dynamic take-profit '
                'and stop-loss levels for each trade. Optimization was '
                'conducted using 14 years of data (from 2010 to 2024) on the '
                'IC Markets server with a Standard account type. \n'
                '\n'
                'Recommendations: \n'
                'Currency Pair: AUDCAD Minimum Deposit: $500 USD Account: H',
 'images': ['https://c.mql5.com/31/1418/monic-logo-200x200-3269.png'],
 'market_id': 'mt5',
 'name': 'Monic',
 'price': 399.0,
 'product_id': '125754',
 'rating': 4.56,
 'reviews_count': 9,
 'scraped_at': '2025-06-13T15:09:26.268002',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladimir Lekhovitser'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'It helps to calculate the risk per trade, the easy '
                'installation of a new order, order management with partial '
                'closing functions, trailing stop of 7 types and other useful '
                'functions. \n'
                'Additional materials and instructions\n'
                'Installation instructions - Application instructions - Trial '
                'version of the application for a demo account \n'
                'Line function - \xa0 shows on the chart the Opening line, '
                'Stop Loss, Take Profit. With this function it is easy to set '
                'a new order and see its additional characteristics bef',
 'images': ['https://c.mql5.com/31/1243/trade-assistant-mt5-logo-200x200-4863.png'],
 'market_id': 'mt5',
 'name': 'Trade Assistant MT5',
 'price': 100.0,
 'product_id': '23415',
 'rating': 4.44,
 'reviews_count': 178,
 'scraped_at': '2025-06-13T15:09:26.270062',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Evgeniy Kravchenko'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'FastWay EA is a smart and efficient automated trading system '
                'built on a powerful mean-reversion strategy. It focuses on '
                'trading correlated currency pairs like AUDCAD, AUDNZD, '
                'NZDCAD, and EURGBP , capitalizing on price returning to its '
                'average after strong directional moves.\n'
                'After purchase, please send a private message to receive full '
                'setup instructions. \n'
                'Live Signal:\xa0 CLICK HERE \n'
                '\n'
                'Post-launch offer: \xa0 Regular price is $1487 , but now '
                'FastWay EA is available at a discount — only $1287 for the n',
 'images': ['https://c.mql5.com/31/1402/fastway-ea-logo-200x200-7424.png'],
 'market_id': 'mt5',
 'name': 'FastWay EA',
 'price': 1287.0,
 'product_id': '139787',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:09:26.271899',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'PAVEL UDOVICHENKO'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'IMPORTANT : This package will only be sold at current price '
                'for a very limited number of copies. \xa0\xa0 Price will go '
                'to 1499$ very fast \xa0\xa0 +100 Strategies included and more '
                'coming! \n'
                'BONUS : At 999$ or higher price --> choose 5 \xa0of my other '
                "EA's for free!\xa0 ALL SET FILES COMPLETE SETUP AND "
                'OPTIMIZATION GUIDE VIDEO GUIDE LIVE SIGNALS REVIEW (3rd '
                'party) \n'
                "Welcome to the ULTIMATE BREAKOUT SYSTEM! I'm pleased to "
                'present the Ultimate Breakout System, a sophisticated and '
                'proprietary Expert Advisor (EA) met',
 'images': ['https://c.mql5.com/31/1420/ultimate-breakout-system-logo-200x200-3928.png'],
 'market_id': 'mt5',
 'name': 'Ultimate Breakout System',
 'price': 999.0,
 'product_id': '133653',
 'rating': 5.0,
 'reviews_count': 12,
 'scraped_at': '2025-06-13T15:09:26.273751',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Profalgo Limited'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 127704: name='Scalping Robot MT5', price=899.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 139303: name='Scalper Investor', price=599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 136033: name='Divergence Bomber', price=99.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 52212: name='Bonnitta EA MT5', price=5500.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 132932: name='GoldenHour', price=239.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Burning Grid EA MT5 – Multi-Pair Grid Power with Adaptive '
                'Risk \n'
                '\n'
                'Trade up to 35 forex pairs simultaneously with intelligent '
                'strategy selection, flexible risk profiles, and dynamic '
                'drawdown control.\n'
                'Actual Price: $999.00 - increases by with next 15 purchases '
                '(Next Price: $1199, Final price: $1999) Contact me to receive '
                'a time-limited, fully functional trial version! \n'
                'Manual: '
                'https://magma-software.solutions/burning-grid/bgmanual-en.html '
                'Community : https://www.mql5.com/en/messages/0151274c579fdb0',
 'images': ['https://c.mql5.com/31/1416/burning-grid-logo-200x200-1710.png'],
 'market_id': 'mt5',
 'name': 'Burning Grid',
 'price': 999.0,
 'product_id': '135273',
 'rating': 4.91,
 'reviews_count': 11,
 'scraped_at': '2025-06-13T15:09:26.278708',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Magma Software Solutions UG'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AI Neuro Dynamics EA Adaptive Signal Architecture for XAU/USD '
                '| H1 AI Neuro Dynamics is more than just an Expert Advisor — '
                'it is a modular cognitive trading system built for precision '
                'and adaptability on the XAU/USD (Gold) pair. Designed for '
                'high-volatility environments, it fully complies with the '
                'performance and risk requirements of prop firm standards. '
                'Powered by a proprietary neuro-quantum decision architecture '
                ', the EA evaluates market structure in real time, dynamically '
                'adjusting its inter',
 'images': ['https://c.mql5.com/31/1376/ai-neuro-dynamics-mt5-logo-200x200-8040.png'],
 'market_id': 'mt5',
 'name': 'AI Neuro Dynamics MT5',
 'price': 599.0,
 'product_id': '134465',
 'rating': 5.0,
 'reviews_count': 6,
 'scraped_at': '2025-06-13T15:09:26.281212',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Peter Robert Grange'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Monitoring of real trading Advisor -\xa0 '
                'https://www.mql5.com/en/signals/2264971 My other '
                'products\xa0 \xa0 -\xa0 \xa0 \xa0 click here Keep in mind '
                'that the results on different brokers may differ, I recommend '
                'testing on your broker before using it\xa0(you can ask me for '
                'a list of recommended brokers in the PM). Read the blog post '
                'with the description of the adviser before starting work and '
                'if you have any additional questions, write to me in the PM. '
                'A fully automatic Expert Advisor that does not require '
                'additional',
 'images': ['https://c.mql5.com/31/1206/king-sniper-ea-logo-200x200-7179.png'],
 'market_id': 'mt5',
 'name': 'King Sniper EA',
 'price': 599.0,
 'product_id': '124990',
 'rating': 5.0,
 'reviews_count': 9,
 'scraped_at': '2025-06-13T15:09:26.284240',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ivan Bebikov'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'We proudly present our cutting-edge robot, the\xa0 Big Forex '
                'Players EA \xa0designed to maximize your trading potential, '
                'minimize emotional trading, and make smarter decisions '
                'powered by cutting-edge technology. The whole system in this '
                'EA took us many months to build, and then we spent a lot of '
                'time testing it.\xa0This unique EA includes three distinct '
                'strategies that can be used independently or in together.\xa0'
                'The robot receives the positions of the\xa0 biggest\xa0'
                'Banks \xa0(positions are sent from our database t',
 'images': ['https://c.mql5.com/31/1042/big-forex-players-mt5-logo-200x200-3102.png'],
 'market_id': 'mt5',
 'name': 'Big Forex Players MT5',
 'price': 1999.0,
 'product_id': '92199',
 'rating': 4.7,
 'reviews_count': 100,
 'scraped_at': '2025-06-13T15:09:26.286709',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 123850: name='GbpUsd Robot MT5', price=499.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 133197: name='AI Gold Sniper MT5', price=399.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 123089: name='Aura Neuron MT5', price=1000.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 138998: name='GoldXpert MT5', price=250.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 113589: name='Way To Stars MT5', price=599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 124335: name='The Gold Phoenix', price=1397.67
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing our advanced Scalping Forex Robot. The scalping '
                'algorithm is built to spot high-probability entry and exit '
                'points, ensuring that every trade is executed with the '
                'highest chance of success within the M1 timeframe . The best '
                'pair to use with the Scalping Robot is XAUUSD .This robot is '
                'perfect for traders who prefer the scalping method and want '
                'to take advantage of rapid price movements without having to '
                'manually monitor the charts. It is suitable for both '
                'beginners looking for an autom',
 'images': ['https://c.mql5.com/31/1243/scalping-robot-mt5-logo-200x200-4701.png'],
 'market_id': 'mt5',
 'name': 'Scalping Robot MT5',
 'price': 899.0,
 'product_id': '127704',
 'rating': 4.37,
 'reviews_count': 65,
 'scraped_at': '2025-06-13T15:09:26.291109',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': "9 copies left at $599 Next price $699 Hey traders, If you're "
                "looking for an EA that doesn't just fire off trades for the "
                'sake of activity, but actually follows a smart, battle-tested '
                'strategy — meet Scalper Investor EA. This is a multi-currency '
                'expert advisor already armed with a solid reversal strategy, '
                'and soon to be upgraded with a trend-following module. Ready '
                'to trade: The Reversal Strategy\n'
                'At launch, Scalper Investor EA comes fully loaded with a '
                'reversal system designed to catch pullbacks',
 'images': ['https://c.mql5.com/31/1395/scalper-investor-logo-200x200-6238.png'],
 'market_id': 'mt5',
 'name': 'Scalper Investor',
 'price': 599.0,
 'product_id': '139303',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:09:26.293230',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'Each buyer of this indicator also receives the following for '
                'free:\n'
                'The custom utility "Bomber Utility", which automatically '
                'manages every trade, sets Stop Loss and Take Profit levels, '
                'and closes trades according to the rules of this strategy Set '
                'files for configuring the indicator for various assets Set '
                'files for configuring Bomber Utility in the following modes: '
                '"Minimum Risk", "Balanced Risk", and "Wait-and-See Strategy" '
                'A step-by-step video manual to help you quickly install, '
                'configure, and s',
 'images': ['https://c.mql5.com/31/1354/divergence-bomber-logo-200x200-2956.png'],
 'market_id': 'mt5',
 'name': 'Divergence Bomber',
 'price': 99.0,
 'product_id': '136033',
 'rating': 5.0,
 'reviews_count': 34,
 'scraped_at': '2025-06-13T15:09:26.295025',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Bonnitta EA \xa0is based on Pending Position strategy ( PPS ) '
                'and a very advanced secretive trading algorithm. The strategy '
                'of\xa0 Bonnitta EA \xa0is a combination of a secretive custom '
                'indicator, Trendlines, Support & Resistance levels ( Price '
                'Action ) and most important secretive trading algorithm '
                "mentioned above. DON'T BUY AN EA WITHOUT ANY REAL MONEY TEST "
                'OF MORE THAN 3 MONTHS, IT TOOK ME MORE THAN 100 WEEKS(MORE '
                'THAN 2 YEARS) TO TEST BONNITTA EA ON REAL MONEY AND SEE THE '
                'RESULT ON THE LINK BELOW. B',
 'images': ['https://c.mql5.com/31/825/bonnitta-ea-mt5-logo-200x200-9279.png'],
 'market_id': 'mt5',
 'name': 'Bonnitta EA MT5',
 'price': 5500.0,
 'product_id': '52212',
 'rating': 3.33,
 'reviews_count': 21,
 'scraped_at': '2025-06-13T15:09:26.296834',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ugochukwu Mobi'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'GoldenHour Expert Advisor\xa0 A precision scalping system for '
                'XAUUSD (Gold) that focuses on high-probability '
                'single-position trades. The EA executes 2-3 carefully '
                'selected trades per day during optimal market conditions, '
                'avoiding risky multi-position or martingale strategies. \n'
                'NEXT price 599$\xa0\xa0 " If the EA doesn\'t perform well in '
                'your backtest, please feel free to message me. I’ll be happy '
                'to help you set it up correctly and get the best possible '
                'results ." \n'
                'Trading Approach: - Single position tra',
 'images': ['https://c.mql5.com/31/1383/goldenhour-logo-200x200-9546.png'],
 'market_id': 'mt5',
 'name': 'GoldenHour',
 'price': 239.0,
 'product_id': '132932',
 'rating': 3.33,
 'reviews_count': 6,
 'scraped_at': '2025-06-13T15:09:26.299101',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Zaha Feiz'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 64961: name='Aura Black Edition MT5', price=1500.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 133492: name='Apex Flow', price=668.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 135571: name='Venom Gold Pro', price=393.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 139184: name='Risk Killer AI MT5', price=399.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 139373: name='AlphaCore X', price=490.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The GBPUSD Robot MT5 is an advanced automated trading system '
                'meticulously designed for the specific dynamics of the \xa0 '
                'GBP/USD \xa0 currency pair. Utilizing advanced technical '
                'analysis, the robot assesses historical and real-time data '
                'to \xa0 identify potential trends , key support and '
                'resistance levels, and other relevant market signals specific '
                'to GBP/USD.\xa0 The Robot opens positions\xa0 every day,\xa0 '
                'from Monday to Friday, and\xa0 all positions are secured \xa0'
                'with Take Profit, Stop Loss, Trailing Stop, Break-E',
 'images': ['https://c.mql5.com/31/1200/gbpusd-robot-mt5-logo-200x200-8029.png'],
 'market_id': 'mt5',
 'name': 'GbpUsd Robot MT5',
 'price': 499.0,
 'product_id': '123850',
 'rating': 4.69,
 'reviews_count': 125,
 'scraped_at': '2025-06-13T15:09:26.303200',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Forex EA Trading Channel on MQL5:\xa0 Join my MQL5 channel to '
                'update the latest news from me.\xa0 My community of over '
                '14,000 members on MQL5 . \n'
                'ONLY 3 COPIES OUT OF 10 LEFT AT $399! After that, the price '
                'will be raised to $499.\n'
                '\n'
                '- REAL SIGNAL\xa0 Low Risk:\xa0 '
                'https://www.mql5.com/en/signals/2302784 \n'
                'IC Markets - High Risk: \xa0 '
                'https://www.mql5.com/en/signals/2310008 \n'
                'Full installation instructions for EA AI Gold Sniper to work '
                'properly are updated at \xa0 comment #3 \n'
                '\n'
                'AI Gold Sniper applies the latest GPT-4o',
 'images': ['https://c.mql5.com/31/1358/ai-gold-scalper-mt5-logo-200x200-8759.png'],
 'market_id': 'mt5',
 'name': 'AI Gold Sniper MT5',
 'price': 399.0,
 'product_id': '133197',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:09:26.305523',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ho Tuan Thang'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Neuron is a distinctive Expert Advisor that continues '
                'the Aura series of trading systems. By leveraging advanced '
                'Neural Networks and cutting-edge classic trading strategies, '
                'Aura Neuron offers an innovative approach with excellent '
                'potential performance. Fully automated, this Expert Advisor '
                'is designed to trade currency pairs such as XAUUSD (GOLD). It '
                'has demonstrated consistent stability across these pairs from '
                '1999 to 2023. The system avoids dangerous money management '
                'techniques, such as m',
 'images': ['https://c.mql5.com/31/1359/aura-neuron-mt5-logo-200x200-4436.png'],
 'market_id': 'mt5',
 'name': 'Aura Neuron MT5',
 'price': 1000.0,
 'product_id': '123089',
 'rating': 4.87,
 'reviews_count': 38,
 'scraped_at': '2025-06-13T15:09:26.307325',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'By popular demand from my large Xpert community (15,000+ '
                'downloads) , I’ve developed a fully automated Expert Advisor '
                'for the highly volatile gold market (XAUUSD) – a powerful '
                'solution for traders looking to capitalize on breakouts, '
                'follow trends, and utilize multiple signals per week . '
                'GoldXpert is designed specifically for beginners and '
                'semi-professionals , offering precise analysis to optimize '
                'your trading strategy and targeting those who want to benefit '
                'from dynamic market movements . Built',
 'images': ['https://c.mql5.com/31/1392/goldxpert-mt5-logo-200x200-1249.png'],
 'market_id': 'mt5',
 'name': 'GoldXpert MT5',
 'price': 250.0,
 'product_id': '138998',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:09:26.309114',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Steve Rosenstock'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Way To Stars is an automated trading system based on the '
                'classic night scalping logic, designed to capture short-term '
                'opportunities during the lowest volatility periods of the '
                'market. \n'
                'Nighttime trading tends to have lower noise and weaker '
                'trends, making it suitable for high-frequency and precise '
                'operations. This type of strategy has existed in the field of '
                'algorithmic trading for over two decades. Way To Stars '
                'inherits this mature framework and rebuilds its algorithm to '
                'fully adapt to current',
 'images': ['https://c.mql5.com/31/1093/way-to-stars-mt5-logo-200x200-9461.png'],
 'market_id': 'mt5',
 'name': 'Way To Stars MT5',
 'price': 599.0,
 'product_id': '113589',
 'rating': 4.73,
 'reviews_count': 30,
 'scraped_at': '2025-06-13T15:09:26.310804',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Wei Tu'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Gold Phoenix GPT - The Ultimate AI Trading Tool for Gold '
                'Pairs Introducing Gold Phoenix GPT, the most thorough and '
                'honest implementation of AI for gold trading. Built on '
                'real-world performance, this EA is specifically designed for '
                'gold pairs, using a powerful breakout strategy on the M1 '
                'timeframe. Unlike many tools that perform well in backtests '
                'but fall short in live conditions, Gold Phoenix GPT excels '
                'where it matters most: in fast-moving live gold markets. '
                'Powered by advanced AI—including Cha',
 'images': ['https://c.mql5.com/31/1219/the-gold-phoenix-g-p-t-logo-200x200-9392.png'],
 'market_id': 'mt5',
 'name': 'The Gold Phoenix',
 'price': 1397.67,
 'product_id': '124335',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:09:26.312672',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 128620: name='Bitcoin Robot Grid MT5', price=599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 131124: name='Golden Algo', price=399.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 138544: name='ScalpPrime', price=499.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 135582: name='Aura Bitcoin Hash', price=675.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 54468: name='EA Gold Stuff mt5', price=50.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Black Edition\xa0is a fully automated EA designed to '
                'trade\xa0GOLD\xa0only. Expert showed stable results on XAUUSD '
                'in 2011-2020 period. No dangerous methods of money management '
                'used, no martingale, no grid or scalp. Suitable for any '
                'broker conditions. EA trained with a\xa0multilayer '
                'perceptron\xa0Neural Network\xa0(MLP) is a class of\xa0'
                'feedforward\xa0artificial neural network\xa0(ANN). The term '
                'MLP is used ambiguously, sometimes loosely to\xa0any\xa0'
                'feedforward ANN, sometimes strictly to refer to networks '
                'composed of mult',
 'images': ['https://c.mql5.com/31/1386/aura-black-edition-mt5-logo-200x200-8408.png'],
 'market_id': 'mt5',
 'name': 'Aura Black Edition MT5',
 'price': 1500.0,
 'product_id': '64961',
 'rating': 4.46,
 'reviews_count': 37,
 'scraped_at': '2025-06-13T15:09:26.317513',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '【突破性US30交易策略】 '
                '这款EA专为US30指数（道琼斯工业平均指数）量身定制，融合先进的时间管理与动态风控技术，为您打造前瞻性且精准高效的交易体验。 '
                '为不同经验水平的交易者提供简便高效的交易体验，并支持个性化设置，满足多样化需求 ，在这里都能找到符合自己需求的操作模式。 '
                '如需查看该策略的交易表现: Live account signal \n'
                '\n'
                '专属资产优化 ：专为US30设计，精准捕捉市场脉动，不适用于其他货币对或资产。 灵活时间框架 '
                '：适用于任意时间框架，随时随地灵活操作。 个性化止损与止盈 '
                '：根据您的风控需求，自定义设置止损、止盈及追踪止损功能，确保风险与收益达到最佳平衡。 动态仓位管理 '
                '：支持固定手数与动态手数分配，满足不同账户规模的需求，提升资金效率。 小资金友好 '
                '：即使是较小资金账户也可顺利运行，体验专业级交易。 平台兼容推荐 ：建议使用对冲账户模式与高流动性平台（例如IC '
                'Market），并推荐在H1时间框架运行 。',
 'images': ['https://c.mql5.com/31/1318/apex-flow-logo-200x200-9015.png'],
 'market_id': 'mt5',
 'name': 'Apex Flow',
 'price': 668.0,
 'product_id': '133492',
 'rating': 5.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:09:26.319904',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dong Zhi Sun'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Venom Gold Pro: Automated XAUUSD M30 EA Venom Gold Pro is a '
                "fully automated Expert Advisor. It's built for XAUUSD (Gold) "
                'on the M30 timeframe . This system uses advanced, pure price '
                'action logic. It monitors market behavior and executes trades '
                'in real-time. No indicators or martingale methods are used. '
                'We focus on clean, rule-based execution. Venom Gold Pro '
                'offers four proven, high-win-rate strategies . You can select '
                'the style that best fits your trading approach. Key Features '
                'Automated Tradin',
 'images': ['https://c.mql5.com/31/1418/venom-gold-pro-logo-200x200-1559.png'],
 'market_id': 'mt5',
 'name': 'Venom Gold Pro',
 'price': 393.0,
 'product_id': '135571',
 'rating': 4.38,
 'reviews_count': 29,
 'scraped_at': '2025-06-13T15:09:26.322034',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antoine Melhem'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Generate consistent returns with a Grok3 AI-assisted , '
                'risk-diversified and Bitcoin-boosted EA . RiskKILLER AI is a '
                'breakout scalping algorithm identifying key levels for '
                'potential high-volatility moves, selecting best risk-reward '
                'trades while diversifying risk on 5 assets. $399: 4 copies '
                'left\xa0 / 10 . After purchase, to get the API key and the '
                'User Manual, 1. post a comment asking for them 2. mail me '
                'directly (mail findable in the dedicated group - see below). '
                '[ Live Signal ] - [\xa0 Specs & Set-up',
 'images': ['https://c.mql5.com/31/1420/risk-killer-ai-mt5-logo-200x200-7662.png'],
 'market_id': 'mt5',
 'name': 'Risk Killer AI MT5',
 'price': 399.0,
 'product_id': '139184',
 'rating': 5.0,
 'reviews_count': 10,
 'scraped_at': '2025-06-13T15:09:26.323932',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Christophe Pa Trouillas'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AlphaCore X The AlphaCore X EA is a cutting-edge trading '
                'system that masters the complexity of financial markets with '
                'a unique combination of AI-driven analyses and data-based '
                'algorithms. By integrating ChatGPT-o1 , the latest GPT-4.5 , '
                'advanced machine learning models, and a robust big data '
                'approach, AlphaCore X achieves a new level of precision, '
                'adaptability, and efficiency. This Expert Advisor impresses '
                'with its innovative strategy, seamless AI interaction, and '
                'comprehensive additional featu',
 'images': ['https://c.mql5.com/31/1396/alphacore-x-logo-200x200-2807.png'],
 'market_id': 'mt5',
 'name': 'AlphaCore X',
 'price': 490.0,
 'product_id': '139373',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:09:26.325842',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Arseny Potyekhin'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 138379: name='INFusion', price=476.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 107189: name='Quantum StarMan', price=499.99
2025-06-13 15:09:26 [market] INFO: Extracted item for product 138199: name='AI ZeroPoint Dynamics MT5', price=499.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 92195: name='XG Gold Robot MT5', price=899.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 39150: name='Forex Trade Manager MT5', price=99.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Bitcoin Robot Grid MT5 is an intelligent trading system '
                'designed to automate BTCUSD trading using the grid trading '
                'strategy. This method takes advantage of market fluctuations '
                'by placing a structured series of buy and sell orders at '
                'predefined price levels. The robot continuously monitors '
                'market conditions and executes trades according to its preset '
                'parameters, allowing for consistent market engagement without '
                'the need for manual intervention. Bitcoin Robot Grid is the '
                'perfect solution for trad',
 'images': ['https://c.mql5.com/31/1256/bitcoin-robot-grid-mt5-logo-200x200-5077.png'],
 'market_id': 'mt5',
 'name': 'Bitcoin Robot Grid MT5',
 'price': 599.0,
 'product_id': '128620',
 'rating': 5.0,
 'reviews_count': 10,
 'scraped_at': '2025-06-13T15:09:26.331343',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Golden Algo\xa0– The Ultimate AI-Powered Expert Advisor for '
                'Gold Traders Golden Algo Expert Advisor is a powerful trading '
                'system designed specifically for XAUUSD (Gold). It combines '
                'technical indicators with real-time market data—including the '
                'US Index and market sentiment—to generate precise trade '
                'signals. Each signal is then filtered through an advanced '
                'OpenAI-powered process to ensure only high-probability trades '
                'are executed. By blending technical analysis, fundamental '
                'insights, and artificial',
 'images': ['https://c.mql5.com/31/1329/golden-algo-logo-200x200-4853.png'],
 'market_id': 'mt5',
 'name': 'Golden Algo',
 'price': 399.0,
 'product_id': '131124',
 'rating': 1.89,
 'reviews_count': 76,
 'scraped_at': '2025-06-13T15:09:26.333796',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ramethara Vijayanathan'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'ScalpPrime \xa0 EA \xa0 \n'
                'Advanced Dual-Strategy Expert Advisor for Structured and '
                'Rule-Based Gold Scalping \xa0 ScalpPrime \xa0 EA is a '
                'precision-engineered Expert Advisor tailored for trading '
                'XAUUSD (Gold) using short-term. It is built on a '
                'professional-grade, rule-based framework that combines two '
                'complementary approaches: a Fibonacci retracement strategy '
                'for \xa0 identifying \xa0 high-probability price zones and a '
                'volume-based confirmation system for filtering market noise '
                'and validating trade entries. \xa0 Desi',
 'images': ['https://c.mql5.com/31/1395/scalpprime-logo-200x200-6661.png'],
 'market_id': 'mt5',
 'name': 'ScalpPrime',
 'price': 499.0,
 'product_id': '138544',
 'rating': 2.88,
 'reviews_count': 25,
 'scraped_at': '2025-06-13T15:09:26.335908',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'DRT Circle'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aura Bitcoin Hash EA is a distinctive Expert Advisor that '
                'continues the Aura series of trading systems. By leveraging '
                'advanced Neural Networks and cutting-edge classic trading '
                'strategies, Aura BTC offers an innovative approach with '
                'excellent potential performance. Fully automated, this Expert '
                'Advisor is designed to trade currency pair BTCUSD (Bitcoin). '
                'It has demonstrated consistent stability across these pairs '
                'from 2017 to 2025. The system avoids dangerous money '
                'management techniques, such as m',
 'images': ['https://c.mql5.com/31/1347/aura-bitcoin-hash-logo-200x200-8237.png'],
 'market_id': 'mt5',
 'name': 'Aura Bitcoin Hash',
 'price': 675.0,
 'product_id': '135582',
 'rating': 4.93,
 'reviews_count': 14,
 'scraped_at': '2025-06-13T15:09:26.337853',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Stanislav Tomilov'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'EA Gold Stuff mt5\xa0is an Expert Advisor designed '
                'specifically for trading gold. The operation is based on '
                'opening orders using the \xa0Gold Stuff mt5 \xa0indicator, '
                'thus the EA works according to the "Trend Follow" strategy, '
                'which means following the trend. For Expert Advisor need '
                'hedge type account\xa0 Contact me immediately after the '
                'purchase to get personal bonus!\xa0 You can get a free copy '
                'of our Strong Support and Trend Scanner indicator, please pm. '
                'me! Settings\xa0 and manual \xa0 here\xa0 \n'
                'Please note that I',
 'images': ['https://c.mql5.com/31/1340/ea-gold-stuff-mt5-logo-200x200-7851.png'],
 'market_id': 'mt5',
 'name': 'EA Gold Stuff mt5',
 'price': 50.0,
 'product_id': '54468',
 'rating': 4.74,
 'reviews_count': 647,
 'scraped_at': '2025-06-13T15:09:26.339861',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vasiliy Strukov'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 109036: name='Eternal Engine EA MT5', price=599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 134062: name='Algo Pumping', price=99.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 140377: name='BreakTrue AI MT5', price=499.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 114522: name='Bitcoin Robot MT5', price=1599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 35049: name='TradePanel MT5', price=100.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Automated Zone-Adaptive Impulse Logic and AI in an EA\n'
                'INFusion is a fully automated Expert Advisor that trades '
                'exclusively the gold market (XAU/USD). Designed for '
                'MetaTrader, the system uses current GPT models\xa0 GPT-o4 '
                'high to analyze price data in real time. The EA combines '
                'modern AI methods with a proprietary price logic, creating a '
                'trading approach that flexibly adapts to market behavior – '
                'without traditional indicators, without martingale, grid or '
                'news trading. INFusion \xa0 is aimed at users s',
 'images': ['https://c.mql5.com/31/1380/quantedgex-logo-200x200-8739.png'],
 'market_id': 'mt5',
 'name': 'INFusion',
 'price': 476.0,
 'product_id': '138379',
 'rating': 5.0,
 'reviews_count': 4,
 'scraped_at': '2025-06-13T15:09:26.344163',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Viktoriya Volgina'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Hello everyone, let me introduce myself:\n'
                '\n'
                'I am Quantum StarMan, the electrifying, freshest member of '
                'the Quantum EAs family. \n'
                '\n'
                "I'm a fully automated, multicurrency EA with the power to "
                'handle up to 5 dynamic pairs: AUDUSD, EURAUD, EURUSD, GBPUSD, '
                'and USDCAD . With the utmost precision and unwavering '
                "responsibility, I'll take your trading game to the next "
                "level. Here's the kicker: I don't rely on Martingale "
                'strategies. Instead, I utilize a sophisticated grid system '
                "that's designed for peak perfor",
 'images': ['https://c.mql5.com/31/1404/quantum-starman-logo-200x200-9143.png'],
 'market_id': 'mt5',
 'name': 'Quantum StarMan',
 'price': 499.99,
 'product_id': '107189',
 'rating': 4.92,
 'reviews_count': 91,
 'scraped_at': '2025-06-13T15:09:26.346358',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Bogdan Ion Puscasu'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'AI ZeroPoint Dynamics EA Cognitive Signal Architecture | '
                'Multi-Asset Precision Engine “Not an EA. Not a strategy. A '
                'living system of inference, adaptation, and execution.” BORN '
                'FROM THE ZERO POINT AI ZeroPoint Dynamics EA is not built — '
                'it is calibrated.\n'
                'Not coded — but architected to function as a real-time '
                'cognitive organism , responding to markets with a depth of '
                'reasoning that mirrors human decision-making — yet surpasses '
                'it in scale, consistency, and velocity. At the heart of '
                'ZeroPoint lie',
 'images': ['https://c.mql5.com/31/1383/ai-zeropoint-dynamics-mt5-logo-200x200-4072.png'],
 'market_id': 'mt5',
 'name': 'AI ZeroPoint Dynamics MT5',
 'price': 499.0,
 'product_id': '138199',
 'rating': 5.0,
 'reviews_count': 7,
 'scraped_at': '2025-06-13T15:09:26.348354',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Peter Robert Grange'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The XG Gold Robot MT5 is specially designed for Gold. We '
                'decided to include this EA in our offering after extensive '
                'testing .\xa0XG Gold Robot and works perfectly with the '
                'XAUUSD, GOLD, XAUEUR pairs. XG Gold Robot has been created '
                'for all traders who like to Trade in Gold and includes '
                'additional a function that displays weekly Gold levels with '
                'the minimum and maximum displayed in the panel as well as on '
                'the chart, which will help you in manual trading. It’s a '
                'strategy based on Price Action, Cycle S',
 'images': ['https://c.mql5.com/31/1058/xg-gold-robot-mt5-logo-200x200-7278.png'],
 'market_id': 'mt5',
 'name': 'XG Gold Robot MT5',
 'price': 899.0,
 'product_id': '92195',
 'rating': 4.27,
 'reviews_count': 83,
 'scraped_at': '2025-06-13T15:09:26.350732',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Welcome to Trade Manager MT5 - the ultimate risk management '
                'tool designed to make trading more intuitive, precise, and '
                "efficient. This is not just an order placement tool; it's a "
                'comprehensive solution for seamless trade planning, position '
                "management, and enhanced control over risk. Whether you're a "
                'beginner taking your first steps, an advanced trader, or a '
                'scalper needing rapid executions, Trade Manager MT5 adapts to '
                'your needs, offering flexibility across all markets, from '
                'forex and indices t',
 'images': ['https://c.mql5.com/31/405/forex-trade-manager-mt5-logo-200x200-4875.png'],
 'market_id': 'mt5',
 'name': 'Forex Trade Manager MT5',
 'price': 99.0,
 'product_id': '39150',
 'rating': 4.98,
 'reviews_count': 522,
 'scraped_at': '2025-06-13T15:09:26.352882',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'InvestSoft'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 136145: name='Venom Us30 Scalp', price=266.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 122975: name='DS Gold Robot MT5', price=599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 129661: name='EvoTrade EA MT5', price=799.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 117023: name='Supply Demand EA ProBot MT5', price=589.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 111357: name='The Gold Reaper MT5', price=599.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Eternal Engine is an advanced EA that integrates multiple '
                'indicators with grid and Martingale strategies. Its core '
                'feature is precise entry point control, enabling it to '
                'perform exceptionally well even in complex market '
                'environments. Eternal Engine EA offers numerous trading '
                'opportunities, is not sensitive to spreads, and ensures '
                'accurate execution of every trade through strict entry point '
                'management. The strategy has been proven in live trading, '
                'providing over a year of low-drawdown real-time s',
 'images': ['https://c.mql5.com/31/1008/eternal-engine-ea-mt5-logo-200x200-9599.png'],
 'market_id': 'mt5',
 'name': 'Eternal Engine EA MT5',
 'price': 599.0,
 'product_id': '109036',
 'rating': 5.0,
 'reviews_count': 17,
 'scraped_at': '2025-06-13T15:09:26.357591',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Wei Tu'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'PUMPING STATION – Your Personal All-inclusive strategy\n'
                'Introducing\xa0PUMPING STATION\xa0— a revolutionary Forex '
                'indicator that will transform your trading into an exciting '
                'and effective activity! This indicator is not just an '
                'assistant but a full-fledged trading system with powerful '
                'algorithms that will help you start trading more stable! When '
                'you purchase this product, you also get FOR FREE: Exclusive '
                'Set Files:\xa0For automatic setup and maximum performance. '
                'Step-by-step video manual:\xa0Learn how to tr',
 'images': ['https://c.mql5.com/31/1323/algo-pumping-logo-200x200-1904.png'],
 'market_id': 'mt5',
 'name': 'Algo Pumping',
 'price': 99.0,
 'product_id': '134062',
 'rating': 5.0,
 'reviews_count': 15,
 'scraped_at': '2025-06-13T15:09:26.359989',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Ihor Otkydach'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'BreakTrue AI: Major levels breakout / rebound trading system '
                'with AI assistance. AI is optional and is used in the way it '
                'is supposed to be. \n'
                'Finally, the Expert Advisor which uses AI in the right way! '
                'BreakTrue AI combines sophisticated built-in trading strategy '
                'based on major levels true and false breakouts, with the '
                'cutting-edge technology of OpenAI’s ChatGPT which servers as '
                'additional entry filter. This isn’t just another empty claim '
                '— BreakTrue AI provides a genuine, fully integrated AI so',
 'images': ['https://c.mql5.com/31/1408/breaktrue-ai-mt5-logo-200x200-7785.png'],
 'market_id': 'mt5',
 'name': 'BreakTrue AI MT5',
 'price': 499.0,
 'product_id': '140377',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:09:26.361968',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Andrey Barinov'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'The Bitcoin Robot MT5 is engineered to execute Bitcoin trades '
                'with unparalleled efficiency and precision . Developed by a '
                'team of experienced traders and developers, our Bitcoin Robot '
                'employs a sophisticated algorithmic approach (price action, '
                'trend as well as two personalized indicators) to analyze '
                'market and execute trades swiftly with M5 timeframe , '
                'ensuring that you never miss out on lucrative opportunities. '
                'No grid, no martingale, no hedging, EA only open one position '
                'at the same time. Bit',
 'images': ['https://c.mql5.com/31/1079/bitcoin-robot-mt5-logo-200x200-2796.png'],
 'market_id': 'mt5',
 'name': 'Bitcoin Robot MT5',
 'price': 1599.0,
 'product_id': '114522',
 'rating': 4.57,
 'reviews_count': 109,
 'scraped_at': '2025-06-13T15:09:26.364251',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Trade Panel is a multifunctional trading assistant. The '
                'application contains more than 50 trading functions for '
                'manual trading and allows you to automate most trading '
                'operations. Instructions for installing the application | '
                'Instructions for the application | Trial version of the '
                'application for a demo account Trade. Allows you to perform '
                'trading operations in one click: Open pending orders and '
                'positions with automatic risk calculation. Open multiple '
                'orders and positions with one click. Open ord',
 'images': ['https://c.mql5.com/31/1369/tradepanel-mt5-logo-200x200-2148.png'],
 'market_id': 'mt5',
 'name': 'TradePanel MT5',
 'price': 100.0,
 'product_id': '35049',
 'rating': 4.84,
 'reviews_count': 133,
 'scraped_at': '2025-06-13T15:09:26.366123',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Alfiya Fazylova'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 128606: name='SmartChoise', price=349.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 140434: name='ARIA Connector EA', price=300.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 128417: name='Prometheus MT5', price=750.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 136885: name='DeepAlgo GPX ML', price=198.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Venom US30 Scalp — Pure Precision for US30 Trading Venom US30 '
                'Scalp is a fully automated Expert Advisor built for US30 (Dow '
                'Jones Index) on the M30 timeframe . It runs on a proprietary '
                'mathematical engine — no indicators, no martingale, no grid — '
                'just clean, logic-based trading. SIGNAL : Ask for signal Core '
                'Features Trend-following strategy with multi-layer '
                'confirmations Default risk: 0.01 lot per $500 (adjustable) '
                'Internal controls to reduce risk exposure Simple '
                'plug-and-play setup, no optimiz',
 'images': ['https://c.mql5.com/31/1366/venom-us30-scalp-logo-200x200-5259.png'],
 'market_id': 'mt5',
 'name': 'Venom Us30 Scalp',
 'price': 266.0,
 'product_id': '136145',
 'rating': 5.0,
 'reviews_count': 9,
 'scraped_at': '2025-06-13T15:09:26.370502',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antoine Melhem'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing the DS Gold Robot, your ultimate companion in '
                'navigating the intricate world of XAUUSD trading. Developed '
                'with precision and powered by cutting-edge algorithms, DS '
                'Gold is a forex robot meticulously crafted to optimize your '
                'trading performance with\xa0 XAUUSD pairs . With its advanced '
                'analytical capabilities,\xa0 DS Gold \xa0Robot \xa0 '
                'constantly monitors the gold market, identifying key trends , '
                'patterns, and price movements with lightning speed. The DS '
                'Gold Robot opens positions every day from',
 'images': ['https://c.mql5.com/31/1182/ds-gold-robot-mt5-logo-200x200-9360.png'],
 'market_id': 'mt5',
 'name': 'DS Gold Robot MT5',
 'price': 599.0,
 'product_id': '122975',
 'rating': 4.25,
 'reviews_count': 24,
 'scraped_at': '2025-06-13T15:09:26.372806',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'MQL TOOLS SL'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'EvoTrade: The First Self-Learning Trading System on the '
                'Market Allow me to introduce EvoTrade —a unique trading '
                'advisor built using cutting-edge technologies in computer '
                'vision and data analysis. It is the first self-learning '
                'trading system on the market, operating in real-time. '
                'EvoTrade analyzes market conditions, adapts strategies, and '
                'dynamically adjusts to changes, delivering exceptional '
                'precision in any environment. EvoTrade employs advanced '
                'neural networks, including Long Short-Term Memory',
 'images': ['https://c.mql5.com/31/1267/evotrade-ea-mt5-logo-200x200-5183.png'],
 'market_id': 'mt5',
 'name': 'EvoTrade EA MT5',
 'price': 799.0,
 'product_id': '129661',
 'rating': 4.67,
 'reviews_count': 18,
 'scraped_at': '2025-06-13T15:09:26.375383',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dolores Martin Munoz'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Fully Automated EA based on Supply and Demand Principles. The '
                'first Supply and Demand EA that is offering Complete '
                'Automation . Now trading Becomes Effortless, offering full '
                'control over your trading strategy through a User-Friendly '
                'graphical Trading Panel. You get a Super High Quality '
                'Algorithmic Trading Software that covers all trading styles '
                'Manual, Semi-Auto and Full-Auto.\xa0Through various settings '
                'and customization options, every trader can create a strategy '
                'that fits their own needs and per',
 'images': ['https://c.mql5.com/31/1110/supply-demand-ea-probot-mt5-logo-200x200-5181.png'],
 'market_id': 'mt5',
 'name': 'Supply Demand EA ProBot MT5',
 'price': 589.0,
 'product_id': '117023',
 'rating': 4.62,
 'reviews_count': 13,
 'scraped_at': '2025-06-13T15:09:26.377465',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Georgios Kalomoiropoulos'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'PROP FIRM READY! ( download SETFILE ) LAUNCH PROMO: Only a '
                'few copies left at current price! Final price: 990$ Get 1 EA '
                'for free (for 2 trade accounts) -> contact me after purchase '
                'Ultimate Combo Deal \xa0 -> \xa0 click here JOIN PUBLIC '
                'GROUP: \xa0 Click here \n'
                'Live Signal\n'
                '\n'
                'Welcome to the Gold Reaper! Build on the very succesfull '
                'Goldtrade Pro, this EA has been designed to run on multiple '
                'timeframes at the same time, and has the option to set the '
                'trade frequency from very conservative to extreme volatile',
 'images': ['https://c.mql5.com/31/1365/the-gold-reaper-mt5-logo-200x200-7714.png'],
 'market_id': 'mt5',
 'name': 'The Gold Reaper MT5',
 'price': 599.0,
 'product_id': '111357',
 'rating': 4.34,
 'reviews_count': 82,
 'scraped_at': '2025-06-13T15:09:26.379422',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Profalgo Limited'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 104565: name='Scalper Deriv', price=380.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 122619: name='Mean Machine', price=1797.53
2025-06-13 15:09:26 [market] INFO: Extracted item for product 140128: name='GoldMasterFusion MT5', price=699.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 68951: name='Local Trade Copier EA MT5', price=50.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 138632: name='TrendX Gold Scalper', price=493.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'SmartChoise EA – Neural Network–Powered Trading System for '
                'XAU/USD (Gold) on M1 Timeframe\xa0\n'
                '\n'
                'The user manual is available via the link in my profile '
                'page.\n'
                'This EA is built for long-term, controlled '
                'growth—understanding and aligning it with your risk tolerance '
                'is key to its success. Uses a neural network–based engine '
                'that continuously analyzes real-time market data to adapt '
                'trading strategies according to current market conditions. '
                'This approach helps optimize trade entries, improve risk '
                'control,',
 'images': ['https://c.mql5.com/31/1305/smartchoise-logo-200x200-7053.png'],
 'market_id': 'mt5',
 'name': 'SmartChoise',
 'price': 349.0,
 'product_id': '128606',
 'rating': 4.05,
 'reviews_count': 37,
 'scraped_at': '2025-06-13T15:09:26.384009',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Gabriel Costin Floricel'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Aria Connector EA (MT5 to ChatGPT and more...) Public '
                'channel:\xa0 https://www.mql5.com/en/channels/binaryforexea \n'
                'Many EAs on the market claim to use artificial intelligence '
                'or "neural networks" when in reality they only run '
                'traditional logic or connect with unreliable sources. Aria '
                'Connector EA was created with a clear and transparent '
                'purpose: to directly connect your MT5 platform with OpenAI’s '
                'AI , with no middlemen or shady scripts. From its first '
                'version, Aria establishes a real connection wit',
 'images': ['https://c.mql5.com/31/1412/aria-connector-ea-logo-200x200-8566.png'],
 'market_id': 'mt5',
 'name': 'ARIA Connector EA',
 'price': 300.0,
 'product_id': '140434',
 'rating': 5.0,
 'reviews_count': 1,
 'scraped_at': '2025-06-13T15:09:26.386526',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Martin Alejandro Bamonte'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Big sale 50% OFF! Price $750. Regular price $1499 All our '
                'signals are now available on myfxbook: \xa0 click here \xa0 '
                'Unique set files and all recommendations are provided free of '
                'charge. All future updates of the adviser are included in the '
                'price. After the purchase, contact me and I will help you '
                'install and configure the robot correctly. I will also share '
                'with you information on how to get a free VPS from a reliable '
                'broker. Gold is one of the riskiest instruments on the '
                'market. It requires precisi',
 'images': ['https://c.mql5.com/31/1394/prometheus-mt5-logo-200x200-1628.png'],
 'market_id': 'mt5',
 'name': 'Prometheus MT5',
 'price': 750.0,
 'product_id': '128417',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:09:26.388884',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Evgenii Aksenov'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'DeepAlgo GPX ML - Real-time Analysis by Intelligent AI '
                'Models \n'
                'When you buy an AI EA, you have no idea if the EA is actually '
                'using AI. With DeepAlgo GPX ML, there are actual API calls '
                'directly to DeepSeek for every single trade and you can see '
                'this reflected on your API dashboard in real time. The API '
                'keys are 100% owned by you and you can manage and use them '
                'however you like. There are multiple fail-safes in place in '
                'order to manage unexpected problems with the API in case the '
                'API server is eve',
 'images': ['https://c.mql5.com/31/1378/deepalgo-gpx-ml-logo-200x200-2368.png'],
 'market_id': 'mt5',
 'name': 'DeepAlgo GPX ML',
 'price': 198.0,
 'product_id': '136885',
 'rating': 5.0,
 'reviews_count': 1,
 'scraped_at': '2025-06-13T15:09:26.393074',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Connor Michael Woodson'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 117386: name='HFT PropFirm EA MT5', price=200.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 139996: name='AI No Stress FX MT5', price=299.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 134947: name='Pure AI', price=199.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 135294: name='PrizmaL Scalper', price=399.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 47785: name='Trend Screener Pro MT5', price=50.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 118127: name='Exp5 AI Sniper for MT5', price=599.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing Scalper Deriv: Elevating Your Scalping '
                'Experience. Important : Scalper Deriv is ONLY sold through '
                'mql5.com. Any other website is a scam and is not Scalper '
                'Deriv.\xa0 To download the updated configuration files and '
                'additional strategies, click here For the user guide, click '
                'here . Are you one of those traders who find their passion in '
                'scalping and want to make the most of your capital? Whether '
                'you have a balance of $20, $200, $2000, $20000, or even '
                '$200000 in your account, we have the p',
 'images': ['https://c.mql5.com/31/1230/scalper-deriv-logo-200x200-9680.png'],
 'market_id': 'mt5',
 'name': 'Scalper Deriv',
 'price': 380.0,
 'product_id': '104565',
 'rating': 4.88,
 'reviews_count': 17,
 'scraped_at': '2025-06-13T15:09:26.399457',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Antonio Simon Del Vecchio'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing Version 8.2—A Revolutionary Leap in AI Trading '
                "Technology I'm proud to announce my most significant update "
                'yet: Version 8.2. This groundbreaking release introduces AI '
                'Position Management and AI Web Search, which dynamically '
                'modifies Take Profit and Stop Loss levels in real-time while '
                'searching the internet for breaking news, political events, '
                'interest rates, and market sentiment, ensuring optimal '
                'position management with priority handling across all '
                'symbols. Version 8.2 harnesses th',
 'images': ['https://c.mql5.com/31/1401/mean-machine-logo-200x200-6801.png'],
 'market_id': 'mt5',
 'name': 'Mean Machine',
 'price': 1797.53,
 'product_id': '122619',
 'rating': 5.0,
 'reviews_count': 33,
 'scraped_at': '2025-06-13T15:09:26.402511',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'William Brandon Autry'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Fusion of Tradition and Innovation—Ushering in a New Era of '
                'Intelligent Trading 【Product Essence】 This EA is specifically '
                'designed for international gold (XAUUSD), ingeniously '
                'combining classic quantitative trading models with modern '
                'intelligent analytical technologies. It deeply explores '
                'market structure and volatility characteristics to achieve '
                'robust and efficient capital growth. The strategy '
                'demonstrates a steadily rising equity curve and low drawdown '
                'in various market environments, making t',
 'images': ['https://c.mql5.com/31/1406/goldmasterfusion-mt5-logo-200x200-6749.png'],
 'market_id': 'mt5',
 'name': 'GoldMasterFusion MT5',
 'price': 699.0,
 'product_id': '140128',
 'rating': 5.0,
 'reviews_count': 3,
 'scraped_at': '2025-06-13T15:09:26.405330',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Chen Jia Qi'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'Experience exceptionally fast trade copying with the \xa0 '
                'Local Trade Copier EA MT5 . With its easy 1-minute setup, '
                'this trade copier allows you to copy trades between multiple '
                'MetaTrader terminals on the same Windows computer or Windows '
                'VPS with lightning-fast copying speeds of under 0.5 seconds. '
                "Whether you're a beginner or a professional trader, the \xa0 "
                'Local Trade Copier EA MT5 \xa0 offers a wide range of options '
                "to customize it to your specific needs. It's the ultimate "
                'solution for anyone looking t',
 'images': ['https://c.mql5.com/31/946/local-trade-copier-ea-mt5-logo-200x200-6081.png'],
 'market_id': 'mt5',
 'name': 'Local Trade Copier EA MT5',
 'price': 50.0,
 'product_id': '68951',
 'rating': 4.98,
 'reviews_count': 83,
 'scraped_at': '2025-06-13T15:09:26.407233',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Juvenille Emperor Limited'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Introducing TrendX Gold Scalper – The Next Evolution in Gold '
                'Trading Automation LAUNCH PROMO:\xa0 Only 2 copies left at '
                'current price. Next price is $553 The price will increase by '
                '$100 with every 10 purchases\xa0 Final price: $1933 \n'
                'LIVE SIGNAL ($100K account) :\xa0 '
                'https://www.mql5.com/en/signals/2304986?source=Site+Profile+Seller '
                'LIVE SIGNAL SMALL:\xa0 '
                'https://www.mql5.com/en/signals/2307472?source=Site+Profile+Seller '
                'LIVE COMBO TrendX + US30 Scalper + EURUSD Algo:\xa0 '
                'https://www.mql5.com/en/signals/23019',
 'images': ['https://c.mql5.com/31/1384/trendx-gold-scalper-logo-200x200-7715.png'],
 'market_id': 'mt5',
 'name': 'TrendX Gold Scalper',
 'price': 493.0,
 'product_id': '138632',
 'rating': 5.0,
 'reviews_count': 1,
 'scraped_at': '2025-06-13T15:09:26.409103',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Lo Thi Mai Loan'}
2025-06-13 15:09:26 [market] INFO: Extracted item for product 136587: name='Spiral Ascend', price=349.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 138518: name='EasyInsight AIO MT5', price=249.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 134248: name='Dax Killer', price=599.0
2025-06-13 15:09:26 [market] INFO: Extracted item for product 133718: name='Zen Flow 2', price=399.0
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'HFT PropFirm EA MT5 is\xa0 also known as Green Man due to its '
                'distinctive logo by Dilwyn Tng, is an Expert Advisor (EA) '
                'crafted specifically for overcoming challenges or evaluations '
                'from proprietary trading firms (prop firms) that permit '
                'High-Frequency Trading (HFT) strategies.\n'
                '\n'
                'Now Greenman\xa0 HFT PropFirm EA MT5 is fully automatic! '
                'Free\xa0 1 All-In-One Breakout EA account licence with '
                'purchase of HFT PropFirm EA MT5 \n'
                'Passing HFT MT5 Challenge Performance Monitor: \n'
                'Broker: Fusion Market\n'
                'Login:\xa0\xa0172147',
 'images': ['https://c.mql5.com/31/1114/hft-propfirm-ea-mt5-logo-200x200-8392.png'],
 'market_id': 'mt5',
 'name': 'HFT PropFirm EA MT5',
 'price': 200.0,
 'product_id': '117386',
 'rating': 4.92,
 'reviews_count': 48,
 'scraped_at': '2025-06-13T15:09:26.412749',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Dilwyn Tng'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'No Stress FX — When Trading Feels a Bit Calmer When I started '
                'building No Stress FX, I wasn’t trying to chase miracles.\n'
                'The goal was to create something that fits a slower, more '
                'mindful style —\n'
                'without pressure, without noise, and hopefully with more '
                'clarity. It’s the kind of trading approach that simply makes '
                'more sense to me. Just being transparent Live Signal:\xa0 '
                'https://www.mql5.com/en/signals/2313926 \n'
                'Yes, it’s running on a real account.\n'
                'Some traders have found it interesting, and activity is',
 'images': ['https://c.mql5.com/31/1407/ai-no-stress-fx-mt5-logo-200x200-9829.png'],
 'market_id': 'mt5',
 'name': 'AI No Stress FX MT5',
 'price': 299.0,
 'product_id': '139996',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:09:26.414583',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Mariia Aborkina'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': '3 copies left at $199\n'
                'Next price $299 Unique trading advisor for EURUSD\n'
                'The advisor is a modular trading system. It is based on an '
                'architecture in which each trading decision is formed not by '
                'a monolithic algorithm, but as a result of the interaction of '
                'independent logical blocks - indicator filters, entry '
                'conditions, exits and control rules. IMPORTANT! After '
                'purchase, send me a private message to receive the '
                'installation guide and setup instructions. Live signal '
                'Strategy XAUUSD - https://www.m',
 'images': ['https://c.mql5.com/31/1335/pure-ai-logo-200x200-4104.png'],
 'market_id': 'mt5',
 'name': 'Pure AI',
 'price': 199.0,
 'product_id': '134947',
 'rating': None,
 'reviews_count': None,
 'scraped_at': '2025-06-13T15:09:26.416571',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vitali Vasilenka'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Live signal \n'
                '\n'
                'Find out more here: \xa0 '
                'https://www.mql5.com/en/users/prizmal/seller \n'
                'PrizmaL Scalper - Intraday Scalping for XAUUSD \n'
                'This trading algorithm is designed for speculative trading in '
                'the spot gold market XAUUSD.\n'
                'It employs advanced market microstructure analysis '
                'techniques, reacting to price impulses and liquidity in real '
                'time. The algorithm is not subject to swaps, making it '
                'particularly effective for active intraday trading.\n'
                'Optimized risk management and dynamic adaptation to volatil',
 'images': ['https://c.mql5.com/31/1388/prizmal-scalper-logo-200x200-7045.png'],
 'market_id': 'mt5',
 'name': 'PrizmaL Scalper',
 'price': 399.0,
 'product_id': '135294',
 'rating': 3.14,
 'reviews_count': 22,
 'scraped_at': '2025-06-13T15:09:26.418387',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladimir Lekhovitser'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Indicators',
 'currency': 'USD',
 'description': 'Unlock the Power of Trends Trading with the Trend Screener '
                'Indicator: Your Ultimate Trend Trading Solution powered by '
                'Fuzzy Logic and Multi-Currencies System! Elevate your trading '
                'game with the Trend Screener, the revolutionary trend '
                'indicator designed to transform your Metatrader into a '
                'powerful Trend Analyzer. This comprehensive tool leverages '
                'fuzzy logic and integrates over 13 premium features and three '
                'trading strategies, offering unmatched precision and '
                'versatility. LIMITED TIME OFFER : Tre',
 'images': ['https://c.mql5.com/31/1414/trend-screener-pro-mt5-logo-200x200-7413.png'],
 'market_id': 'mt5',
 'name': 'Trend Screener Pro MT5',
 'price': 50.0,
 'product_id': '47785',
 'rating': 4.86,
 'reviews_count': 84,
 'scraped_at': '2025-06-13T15:09:26.420264',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'STE S.S.COMPANY'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Our team is thrilled to introduce Trading Robot, the '
                'cutting-edge Smart Trading Expert Advisor for the MetaTrader '
                'terminal. AI Sniper is an intelligent, self-optimizing '
                'trading robot designed for MT5 . It leverages a smart '
                'algorithm and advanced trading strategies to maximize your '
                'trading potential. With 15 years of experience in trading '
                'exchanges and the stock market, we have developed innovative '
                'strategy management features, additional intelligent '
                'functions, and a user-friendly graphical inte',
 'images': ['https://c.mql5.com/31/1268/exp5-ai-sniper-for-mt5-logo-200x200-7487.png'],
 'market_id': 'mt5',
 'name': 'Exp5 AI Sniper for MT5',
 'price': 599.0,
 'product_id': '118127',
 'rating': 4.0,
 'reviews_count': 2,
 'scraped_at': '2025-06-13T15:09:26.422479',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Vladislav Andruschenko'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'Spiral Ascend EA – Smart Automated Trading for XAUUSD \n'
                'The\xa0 Spiral Ascend \xa0 EA is a powerful, fully automated '
                'trading solution tailored for the XAUUSD (Gold) market , '
                'combining classical Fibonacci methodologies with modern '
                'technical analysis and advanced volume-based logic. Developed '
                'with flexibility and precision in mind, this EA is ideal for '
                'traders seeking a disciplined and logic-driven approach to '
                'algorithmic trading—including those working under the '
                'conditions of prop trading firms . Buy S',
 'images': ['https://c.mql5.com/31/1368/spiral-ascend-logo-200x200-4715.png'],
 'market_id': 'mt5',
 'name': 'Spiral Ascend',
 'price': 349.0,
 'product_id': '136587',
 'rating': 4.93,
 'reviews_count': 14,
 'scraped_at': '2025-06-13T15:09:26.426893',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'DRT Circle'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Utilities',
 'currency': 'USD',
 'description': 'EASY Insight AIO – All-in-One Market Scanner with Full '
                'Indicator Data What if you could analyze the entire Forex '
                'market in seconds – without plotting anything on your '
                'charts? \n'
                'EASY Insight AIO integrates the full analytical output from '
                'FX Power, FX Volume, FX Dynamic, and FX Levels – into a '
                'single, structured CSV file. No extra tools needed. No chart '
                'overlays. No distractions. \n'
                '⸻ \n'
                'Why Use EASY Insight AIO? All Indicators Included: No need '
                'for additional licenses – AIO comes with full access to',
 'images': ['https://c.mql5.com/31/1384/easyinsight-aio-mt5-logo-200x200-8116.png'],
 'market_id': 'mt5',
 'name': 'EasyInsight AIO MT5',
 'price': 249.0,
 'product_id': '138518',
 'rating': 5.0,
 'reviews_count': 4,
 'scraped_at': '2025-06-13T15:09:26.428922',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Alain Verleyen'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'After 6 Years of Successful Manual Trading, My Strategies Are '
                'Now Available as Expert Advisors! \n'
                'Introducing the DAX Killer EA – a trading system built for '
                'the DAX Index from years of hands-on experience, extensive '
                'testing, and a steadfast commitment to secure, strategic '
                'trading. NO GRID, NO MARTINGALE, TIGHT SL EVERY TRADE. ONE '
                'TRADE PER DAY . \xa0 NO LOT MULTIPLIER. \xa0The price of the '
                'EA will increase by $100 with every 10 purchases. ICTRADING '
                'LIVE SIGNAL \xa0 DAX Killer Public \xa0 Chat \xa0 Group \xa0 '
                'IMPOR',
 'images': ['https://c.mql5.com/31/1337/dax-killer-logo-200x200-2452.png'],
 'market_id': 'mt5',
 'name': 'Dax Killer',
 'price': 599.0,
 'product_id': '134248',
 'rating': 3.67,
 'reviews_count': 3,
 'scraped_at': '2025-06-13T15:09:26.431011',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Pablo Dominguez Sanchez'}
2025-06-13 15:09:26 [scrapy.core.scraper] WARNING: Dropped: Duplicate item found: market_mt5
{'category': 'Experts',
 'currency': 'USD',
 'description': 'LAUNCH PROMO: Final price: 1,700$ Only 2 copies left at $399. '
                'Next price will be $499 Get 1 EA for free (for 2 trade '
                'accounts) -> contact me after purchase Instruction Blog Link '
                'to Channel \n'
                'Welcome to ZenFlow! ZenFlow is an advanced EA designed to '
                'adapt to changing market trends with precision and speed. It '
                'is optimized to trade the XAUUSD( or GOLD) symbol and should '
                'be run on only one chart. This EA uses a sophisticated '
                'trend-following strategy combined with a momentum-based '
                'indicator that ide',
 'images': ['https://c.mql5.com/31/1318/zen-flow-2-logo-200x200-9786.png'],
 'market_id': 'mt5',
 'name': 'Zen Flow 2',
 'price': 399.0,
 'product_id': '133718',
 'rating': 2.72,
 'reviews_count': 29,
 'scraped_at': '2025-06-13T15:09:26.432844',
 'url': 'https://www.mql5.com/en/market/mt5',
 'vendor': 'Hamza Ashraf'}
2025-06-13 15:09:26 [scrapy.core.engine] INFO: Closing spider (finished)
2025-06-13 15:09:26 [market] INFO: Exported 1 items to output/market_20250613_150926.json
2025-06-13 15:09:26 [scrapy.statscollectors] INFO: Dumping Scrapy stats:
{'downloader/request_bytes': 306,
 'downloader/request_count': 1,
 'downloader/request_method_count/GET': 1,
 'downloader/response_bytes': 55027,
 'downloader/response_count': 1,
 'downloader/response_status_count/200': 1,
 'elapsed_time_seconds': 0.332143,
 'finish_reason': 'finished',
 'finish_time': datetime.datetime(2025, 6, 13, 13, 9, 26, 440111, tzinfo=datetime.timezone.utc),
 'httpcache/hit': 1,
 'httpcompression/response_bytes': 210210,
 'httpcompression/response_count': 1,
 'item_dropped_count': 69,
 'item_dropped_reasons_count/DropItem': 69,
 'item_scraped_count': 1,
 'items_per_minute': None,
 'log_count/INFO': 81,
 'log_count/WARNING': 73,
 'memusage/max': 75309056,
 'memusage/startup': 75309056,
 'response_received_count': 1,
 'responses_per_minute': None,
 'scheduler/dequeued': 1,
 'scheduler/dequeued/memory': 1,
 'scheduler/enqueued': 1,
 'scheduler/enqueued/memory': 1,
 'start_time': datetime.datetime(2025, 6, 13, 13, 9, 26, 107968, tzinfo=datetime.timezone.utc)}
2025-06-13 15:09:26 [scrapy.core.engine] INFO: Spider closed (finished)
