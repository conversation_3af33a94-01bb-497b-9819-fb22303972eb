# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Voyagr Scrapy is a web scraping project built with Scrapy and FastAPI. It extracts four types of data: market information, product information, user information, and developer information. The project is packaged with uv and provides a REST API interface for running scraping jobs.

## Key Commands

### Environment Setup
```bash
uv sync                    # Install dependencies and sync environment
uv add <package>          # Add new dependencies
```

### Running the API Server
```bash
uv run python src/voyagr_scrapy/api.py
# or
uv run uvicorn src.voyagr_scrapy.api:app --reload --host 0.0.0.0 --port 8000
```

### Running Scrapy Spiders Directly
```bash
uv run scrapy crawl market -a start_urls=https://example.com
uv run scrapy crawl product -a start_urls=https://example.com,https://example2.com
uv run scrapy crawl user -a start_urls=https://example.com -a allowed_domains=example.com
uv run scrapy crawl developer -a start_urls=https://example.com
```

### Testing and Validation
```bash
uv run scrapy check          # Check spiders for errors
uv run scrapy list           # List available spiders
```

## Project Architecture

### Core Components

1. **Spiders** (`src/voyagr_scrapy/spiders/`):
   - `market_spider.py`: Extracts marketplace information
   - `product_spider.py`: Extracts product details and specifications
   - `user_spider.py`: Extracts user profiles and information
   - `developer_spider.py`: Extracts developer/vendor profiles

2. **Data Models** (`src/voyagr_scrapy/items.py`):
   - `MarketItem`: Market/marketplace data structure
   - `ProductItem`: Product data with pricing, specifications, reviews
   - `UserItem`: User profile data with reputation and activity
   - `DeveloperItem`: Developer profile with portfolio and certifications

3. **Pipelines** (`src/voyagr_scrapy/pipelines/`):
   - `ValidationPipeline`: Validates required fields
   - `DeduplicationPipeline`: Removes duplicate items
   - `JsonExportPipeline`: Exports data to JSON files

4. **FastAPI Wrapper** (`src/voyagr_scrapy/api.py`):
   - REST API for running scraping jobs
   - Background task management
   - Job status tracking and result retrieval

### Key Design Patterns

- **Spider Inheritance**: All spiders inherit from `scrapy.Spider` with common initialization patterns
- **Item Field Mapping**: Each spider type has specific item fields mapped to extracted data
- **Pipeline Chain**: Data flows through validation → deduplication → export
- **Async Job Management**: FastAPI handles background scraping tasks with status tracking

### Spider Customization

Each spider accepts runtime parameters:
- `start_urls`: Comma-separated list of URLs to scrape
- `allowed_domains`: Comma-separated list of allowed domains
- Custom extraction methods can be overridden for specific sites

### Data Flow

1. API receives scraping request
2. Background task spawns Scrapy spider process
3. Spider extracts data into Items
4. Items flow through pipeline (validation → deduplication → export)
5. Results saved to JSON files in `output/` directory
6. API provides job status and result retrieval endpoints

### Configuration

- `settings.py`: Scrapy configuration with pipelines, delays, and middlewares
- `middlewares.py`: User agent rotation and proxy support
- `scrapy.cfg`: Scrapy project configuration file

## Output Structure

Results are saved as JSON files in `output/` directory with naming pattern:
`{spider_name}_{timestamp}.json`

Each output file contains an array of extracted items with all fields populated according to the respective Item class.